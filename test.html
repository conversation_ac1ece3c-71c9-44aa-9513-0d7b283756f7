<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VS Code Editor 测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .feature-list {
            list-style-type: none;
            padding: 0;
        }
        .feature-list li {
            padding: 10px;
            margin: 5px 0;
            background: #e8f4fd;
            border-left: 4px solid #007acc;
            border-radius: 4px;
        }
        .test-button {
            background: #007acc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #005a9e;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>VS Code 风格编辑器测试页面</h1>
        
        <h2>功能特性</h2>
        <ul class="feature-list">
            <li>✅ VS Code 风格的深色主题界面</li>
            <li>✅ 活动栏和侧边栏布局</li>
            <li>✅ 文件资源管理器</li>
            <li>✅ 标签页切换</li>
            <li>✅ 代码编辑器</li>
            <li>✅ 行号显示</li>
            <li>✅ 状态栏</li>
            <li>✅ 响应式设计（PC和移动端）</li>
            <li>✅ 键盘快捷键支持</li>
            <li>✅ 文件管理功能</li>
            <li>✅ 菜单栏下拉菜单</li>
            <li>✅ 完整的编辑功能</li>
            <li>✅ 多级文件夹展开收缩</li>
            <li>✅ 文件树右键菜单</li>
            <li>✅ 文件夹和文件管理</li>
        </ul>

        <h2>测试功能</h2>
        <p>请在编辑器中测试以下功能：</p>
        
        <button class="test-button" onclick="testFileOperations()">测试文件操作</button>
        <button class="test-button" onclick="testFileTree()">测试文件树</button>
        <button class="test-button" onclick="testKeyboardShortcuts()">测试键盘快捷键</button>
        <button class="test-button" onclick="testMenus()">测试菜单功能</button>
        <button class="test-button" onclick="testResponsiveDesign()">测试响应式设计</button>
        
        <h2>键盘快捷键</h2>
        <ul>
            <li><strong>Ctrl+N</strong> - 新建文件</li>
            <li><strong>Ctrl+O</strong> - 打开文件</li>
            <li><strong>Ctrl+S</strong> - 保存文件</li>
            <li><strong>Ctrl+W</strong> - 关闭标签页</li>
            <li><strong>Ctrl+F</strong> - 搜索</li>
            <li><strong>Ctrl+H</strong> - 替换</li>
            <li><strong>Ctrl+B</strong> - 切换侧边栏</li>
            <li><strong>Ctrl+G</strong> - 转到行</li>
            <li><strong>Ctrl+P</strong> - 转到文件</li>
            <li><strong>Ctrl+L</strong> - 选择行</li>
            <li><strong>Ctrl++</strong> - 放大</li>
            <li><strong>Ctrl+-</strong> - 缩小</li>
            <li><strong>Ctrl+0</strong> - 重置缩放</li>
            <li><strong>F5</strong> - 运行文件</li>
            <li><strong>Esc</strong> - 关闭菜单/侧边栏</li>
        </ul>

        <h2>使用说明</h2>
        <ol>
            <li>点击顶部菜单栏项目查看下拉菜单</li>
            <li>点击左侧活动栏的图标切换不同面板</li>
            <li>在文件资源管理器中点击文件夹前的箭头展开/收缩文件夹</li>
            <li>点击文件名打开文件，右键文件或文件夹查看更多选项</li>
            <li>使用标签页在不同文件间切换</li>
            <li>在代码编辑器中编写代码</li>
            <li>使用键盘快捷键提高效率</li>
            <li>在移动设备上，点击活动栏图标打开侧边栏</li>
        </ol>
    </div>

    <script>
        function testFileOperations() {
            alert('请在编辑器中：\n1. 点击文件资源管理器中的 "+" 图标创建新文件\n2. 点击文件名打开文件\n3. 点击标签页的 "×" 关闭文件');
        }

        function testKeyboardShortcuts() {
            alert('请在编辑器中测试：\n1. Ctrl+S 保存文件\n2. Ctrl+N 新建文件\n3. Ctrl+W 关闭标签页\n4. Ctrl+F 打开搜索\n5. Ctrl+G 转到行\n6. Ctrl+B 切换侧边栏');
        }

        function testFileTree() {
            alert('请在编辑器中测试：\n1. 点击文件夹前的箭头展开/收缩文件夹\n2. 右键点击文件或文件夹查看菜单\n3. 尝试创建新文件或文件夹\n4. 尝试重命名或删除项目\n5. 点击文件名打开文件');
        }

        function testMenus() {
            alert('请在编辑器中测试：\n1. 点击顶部菜单栏的"文件"查看下拉菜单\n2. 尝试"文件" > "新建文件"\n3. 尝试"编辑" > "查找"\n4. 尝试"查看" > "放大"\n5. 点击其他地方关闭菜单');
        }

        function testResponsiveDesign() {
            alert('请调整浏览器窗口大小或使用开发者工具的设备模拟器测试移动端显示效果');
        }
    </script>
</body>
</html>
