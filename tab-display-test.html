<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>标签页显示测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-step {
            background: #e8f4fd;
            border-left: 4px solid #007acc;
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .test-step h3 {
            margin: 0 0 10px 0;
            color: #007acc;
        }
        .success {
            background: #d4edda;
            border-left-color: #28a745;
        }
        .success h3 {
            color: #28a745;
        }
        .warning {
            background: #fff3cd;
            border-left-color: #ffc107;
        }
        .warning h3 {
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>标签页显示修复测试</h1>
        
        <p>测试修复后的标签页显示功能，确保只同时显示一个标签页内容。</p>

        <div class="test-step">
            <h3>问题描述</h3>
            <p>之前的问题：代码编辑区域可能同时显示多个标签页内容，导致界面混乱。</p>
            <p>修复目标：确保任何时候只显示一个标签页内容（欢迎页面或一个代码编辑器）。</p>
        </div>

        <div class="test-step">
            <h3>修复内容</h3>
            <p><strong>1. CSS样式修复：</strong></p>
            <ul>
                <li>使用绝对定位确保标签页内容完全重叠</li>
                <li>只有active类的标签页才显示</li>
                <li>非active标签页强制隐藏</li>
            </ul>
            <p><strong>2. JavaScript逻辑强化：</strong></p>
            <ul>
                <li>showTab方法强制隐藏所有标签页</li>
                <li>使用style.display确保显示状态</li>
                <li>移除HTML中的默认active类</li>
            </ul>
        </div>

        <div class="test-step">
            <h3>测试步骤</h3>
            <p><strong>步骤 1：检查初始状态</strong></p>
            <ul>
                <li>打开编辑器，应该只显示欢迎页面</li>
                <li>不应该看到任何代码编辑器内容</li>
            </ul>
            
            <p><strong>步骤 2：测试标签页切换</strong></p>
            <ul>
                <li>点击文件树中的文件，打开代码编辑器</li>
                <li>应该只显示代码编辑器，欢迎页面完全隐藏</li>
                <li>切换回欢迎标签页，应该只显示欢迎页面</li>
            </ul>
            
            <p><strong>步骤 3：测试多个文件</strong></p>
            <ul>
                <li>打开多个文件，创建多个标签页</li>
                <li>在不同标签页间切换</li>
                <li>每次只应该显示一个编辑器内容</li>
            </ul>
        </div>

        <div class="test-step success">
            <h3>预期结果</h3>
            <p>✅ 任何时候只显示一个标签页内容</p>
            <p>✅ 标签页切换时内容正确切换</p>
            <p>✅ 没有内容重叠或混乱</p>
            <p>✅ 欢迎页面和代码编辑器正确切换</p>
            <p>✅ 界面布局整洁清晰</p>
        </div>

        <div class="test-step warning">
            <h3>如果仍有问题</h3>
            <p>⚠️ 检查浏览器开发者工具中的CSS样式</p>
            <p>⚠️ 确认JavaScript没有报错</p>
            <p>⚠️ 检查.editor-tab元素的display属性</p>
            <p>⚠️ 确认只有一个元素有active类</p>
        </div>

        <h2>技术实现细节</h2>
        
        <h3>CSS修复</h3>
        <pre style="background: #f8f9fa; padding: 15px; border-radius: 4px; overflow-x: auto;">
/* 编辑器标签页 */
.editor-tab {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: none;
    background-color: #1e1e1e;
    overflow: hidden;
}

.editor-tab.active {
    display: flex;
    z-index: 1;
}</pre>

        <h3>JavaScript修复</h3>
        <pre style="background: #f8f9fa; padding: 15px; border-radius: 4px; overflow-x: auto;">
showTab(fileName) {
    // 强制隐藏所有标签页内容
    document.querySelectorAll('.editor-tab').forEach(tab => {
        tab.classList.remove('active');
        tab.style.display = 'none';
    });
    
    // 显示指定标签页
    if (fileName === 'welcome') {
        const welcomePage = document.getElementById('welcomePage');
        welcomePage.classList.add('active');
        welcomePage.style.display = 'flex';
    } else {
        const editorTab = document.querySelector(`[data-file="${fileName}"].editor-tab`);
        if (editorTab) {
            editorTab.classList.add('active');
            editorTab.style.display = 'flex';
        }
    }
}</pre>

        <h2>关键改进点</h2>
        <ul>
            <li><strong>绝对定位</strong>：确保所有标签页内容在同一位置</li>
            <li><strong>强制隐藏</strong>：使用JavaScript强制设置display属性</li>
            <li><strong>单一显示</strong>：确保只有一个标签页有active类</li>
            <li><strong>z-index管理</strong>：活动标签页有更高的层级</li>
        </ul>
    </div>
</body>
</html>
