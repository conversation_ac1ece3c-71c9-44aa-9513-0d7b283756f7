<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>标签页右键菜单测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-step {
            background: #e8f4fd;
            border-left: 4px solid #007acc;
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .test-step h3 {
            margin: 0 0 10px 0;
            color: #007acc;
        }
        .test-step p {
            margin: 5px 0;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .feature-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
        }
        .feature-card h4 {
            margin: 0 0 10px 0;
            color: #495057;
        }
        .feature-card ul {
            margin: 0;
            padding-left: 20px;
        }
        .success {
            background: #d4edda;
            border-left-color: #28a745;
        }
        .success h3 {
            color: #28a745;
        }
        .warning {
            background: #fff3cd;
            border-left-color: #ffc107;
        }
        .warning h3 {
            color: #856404;
        }
        .menu-item {
            display: flex;
            align-items: center;
            padding: 8px 12px;
            background: #2d2d30;
            color: #cccccc;
            margin: 5px 0;
            border-radius: 3px;
            font-size: 13px;
        }
        .menu-item i {
            margin-right: 8px;
            width: 16px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>标签页右键菜单功能测试</h1>
        
        <p>VS Code编辑器现在支持完整的标签页右键菜单功能，提供强大的批量标签页操作能力。</p>

        <h2>功能概览</h2>
        <div class="feature-grid">
            <div class="feature-card">
                <h4>🎯 基础操作</h4>
                <ul>
                    <li>关闭当前标签</li>
                    <li>复制文件路径</li>
                    <li>在资源管理器中显示</li>
                </ul>
            </div>
            <div class="feature-card">
                <h4>📋 批量操作</h4>
                <ul>
                    <li>关闭其他标签</li>
                    <li>关闭左边标签</li>
                    <li>关闭右边标签</li>
                    <li>关闭所有标签</li>
                </ul>
            </div>
            <div class="feature-card">
                <h4>🧠 智能功能</h4>
                <ul>
                    <li>根据位置启用/禁用菜单项</li>
                    <li>自动判断标签页数量</li>
                    <li>防止误操作的状态管理</li>
                </ul>
            </div>
        </div>

        <h2>右键菜单项目</h2>
        <div style="background: #1e1e1e; padding: 15px; border-radius: 8px; margin: 15px 0;">
            <div class="menu-item">
                <i class="fas fa-times"></i>
                <span>关闭当前标签</span>
            </div>
            <div style="height: 1px; background: #464647; margin: 4px 0;"></div>
            <div class="menu-item">
                <i class="fas fa-times-circle"></i>
                <span>关闭其他标签</span>
            </div>
            <div class="menu-item">
                <i class="fas fa-arrow-left"></i>
                <span>关闭左边标签</span>
            </div>
            <div class="menu-item">
                <i class="fas fa-arrow-right"></i>
                <span>关闭右边标签</span>
            </div>
            <div style="height: 1px; background: #464647; margin: 4px 0;"></div>
            <div class="menu-item">
                <i class="fas fa-ban"></i>
                <span>关闭所有标签</span>
            </div>
            <div style="height: 1px; background: #464647; margin: 4px 0;"></div>
            <div class="menu-item">
                <i class="fas fa-copy"></i>
                <span>复制文件路径</span>
            </div>
            <div class="menu-item">
                <i class="fas fa-folder-open"></i>
                <span>在资源管理器中显示</span>
            </div>
        </div>

        <h2>测试步骤</h2>

        <div class="test-step">
            <h3>步骤 1: 准备测试环境</h3>
            <p>1. 打开VS Code编辑器</p>
            <p>2. 在文件资源管理器中点击多个文件，创建至少4-5个标签页</p>
            <p>3. 确保有足够的标签页来测试各种批量操作</p>
        </div>

        <div class="test-step">
            <h3>步骤 2: 测试基础右键菜单</h3>
            <p>1. 右键点击任意标签页，确保右键菜单正常显示</p>
            <p>2. 检查菜单项是否完整显示</p>
            <p>3. 点击其他地方确保菜单能正常关闭</p>
        </div>

        <div class="test-step">
            <h3>步骤 3: 测试关闭当前标签</h3>
            <p>1. 右键点击某个标签页</p>
            <p>2. 选择"关闭当前标签"</p>
            <p>3. 确认该标签页被关闭，其他标签页保持打开</p>
        </div>

        <div class="test-step">
            <h3>步骤 4: 测试关闭其他标签</h3>
            <p>1. 确保有多个标签页打开</p>
            <p>2. 右键点击其中一个标签页</p>
            <p>3. 选择"关闭其他标签"</p>
            <p>4. 确认只有右键点击的标签页保留，其他都被关闭</p>
        </div>

        <div class="test-step">
            <h3>步骤 5: 测试关闭左边/右边标签</h3>
            <p>1. 重新打开多个标签页</p>
            <p>2. 右键点击中间位置的某个标签页</p>
            <p>3. 选择"关闭左边标签"，确认左侧标签页被关闭</p>
            <p>4. 选择"关闭右边标签"，确认右侧标签页被关闭</p>
        </div>

        <div class="test-step">
            <h3>步骤 6: 测试智能启用/禁用</h3>
            <p>1. 右键点击最左边的标签页，确认"关闭左边标签"被禁用</p>
            <p>2. 右键点击最右边的标签页，确认"关闭右边标签"被禁用</p>
            <p>3. 当只有一个标签页时，确认"关闭其他标签"被禁用</p>
        </div>

        <div class="test-step">
            <h3>步骤 7: 测试辅助功能</h3>
            <p>1. 右键点击标签页，选择"复制文件路径"</p>
            <p>2. 检查控制台确认路径已复制</p>
            <p>3. 选择"在资源管理器中显示"</p>
            <p>4. 确认文件在文件树中被高亮显示</p>
        </div>

        <div class="test-step success">
            <h3>预期结果</h3>
            <p>✅ 右键菜单正常显示和隐藏</p>
            <p>✅ 所有批量关闭功能正常工作</p>
            <p>✅ 菜单项根据上下文智能启用/禁用</p>
            <p>✅ 辅助功能（复制路径、显示位置）正常工作</p>
            <p>✅ 操作后编辑器状态正确更新</p>
        </div>

        <div class="test-step warning">
            <h3>注意事项</h3>
            <p>⚠️ 确保右键点击的是标签页区域，不是关闭按钮</p>
            <p>⚠️ 批量关闭操作不可撤销，请谨慎测试</p>
            <p>⚠️ 如果遇到问题，请检查浏览器控制台的错误信息</p>
        </div>

        <h2>技术实现亮点</h2>
        <ul>
            <li><strong>智能状态管理</strong>：根据标签页位置和数量动态启用/禁用菜单项</li>
            <li><strong>批量操作优化</strong>：高效的批量关闭算法，避免状态冲突</li>
            <li><strong>用户体验</strong>：符合VS Code原生行为的交互设计</li>
            <li><strong>防误操作</strong>：禁用不可用的操作，防止用户困惑</li>
            <li><strong>上下文感知</strong>：菜单内容根据当前标签页状态动态调整</li>
        </ul>
    </div>
</body>
</html>
