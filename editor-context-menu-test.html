<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>代码编辑器右键菜单测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-step {
            background: #e8f4fd;
            border-left: 4px solid #007acc;
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .test-step h3 {
            margin: 0 0 10px 0;
            color: #007acc;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .feature-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
        }
        .feature-card h4 {
            margin: 0 0 10px 0;
            color: #495057;
        }
        .menu-preview {
            background: #1e1e1e;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .menu-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 8px 12px;
            background: #2d2d30;
            color: #cccccc;
            margin: 2px 0;
            border-radius: 3px;
            font-size: 13px;
        }
        .menu-item i {
            margin-right: 8px;
            width: 16px;
        }
        .shortcut {
            color: #858585;
            font-size: 11px;
        }
        .separator {
            height: 1px;
            background: #464647;
            margin: 4px 0;
        }
        .success {
            background: #d4edda;
            border-left-color: #28a745;
        }
        .success h3 {
            color: #28a745;
        }
        .warning {
            background: #fff3cd;
            border-left-color: #ffc107;
        }
        .warning h3 {
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>代码编辑器右键菜单功能测试</h1>
        
        <p>VS Code编辑器现在支持完整的代码编辑器右键菜单功能，提供专业的代码编辑体验。</p>

        <h2>功能概览</h2>
        <div class="feature-grid">
            <div class="feature-card">
                <h4>📝 基础编辑</h4>
                <ul>
                    <li>撤销/重做</li>
                    <li>剪切/复制/粘贴</li>
                    <li>全选/选择行</li>
                </ul>
            </div>
            <div class="feature-card">
                <h4>🔍 查找替换</h4>
                <ul>
                    <li>查找文本</li>
                    <li>替换文本</li>
                    <li>转到指定行</li>
                </ul>
            </div>
            <div class="feature-card">
                <h4>🛠️ 代码工具</h4>
                <ul>
                    <li>切换注释</li>
                    <li>格式化文档</li>
                    <li>智能缩进</li>
                </ul>
            </div>
            <div class="feature-card">
                <h4>🧠 智能功能</h4>
                <ul>
                    <li>根据选择状态启用/禁用</li>
                    <li>剪贴板集成</li>
                    <li>键盘快捷键支持</li>
                </ul>
            </div>
        </div>

        <h2>右键菜单预览</h2>
        <div class="menu-preview">
            <div class="menu-item">
                <span><i class="fas fa-undo"></i>撤销</span>
                <span class="shortcut">Ctrl+Z</span>
            </div>
            <div class="menu-item">
                <span><i class="fas fa-redo"></i>重做</span>
                <span class="shortcut">Ctrl+Y</span>
            </div>
            <div class="separator"></div>
            <div class="menu-item">
                <span><i class="fas fa-cut"></i>剪切</span>
                <span class="shortcut">Ctrl+X</span>
            </div>
            <div class="menu-item">
                <span><i class="fas fa-copy"></i>复制</span>
                <span class="shortcut">Ctrl+C</span>
            </div>
            <div class="menu-item">
                <span><i class="fas fa-paste"></i>粘贴</span>
                <span class="shortcut">Ctrl+V</span>
            </div>
            <div class="separator"></div>
            <div class="menu-item">
                <span><i class="fas fa-check-square"></i>全选</span>
                <span class="shortcut">Ctrl+A</span>
            </div>
            <div class="menu-item">
                <span><i class="fas fa-minus"></i>选择行</span>
                <span class="shortcut">Ctrl+L</span>
            </div>
            <div class="separator"></div>
            <div class="menu-item">
                <span><i class="fas fa-search"></i>查找</span>
                <span class="shortcut">Ctrl+F</span>
            </div>
            <div class="menu-item">
                <span><i class="fas fa-exchange-alt"></i>替换</span>
                <span class="shortcut">Ctrl+H</span>
            </div>
            <div class="separator"></div>
            <div class="menu-item">
                <span><i class="fas fa-arrow-right"></i>转到行</span>
                <span class="shortcut">Ctrl+G</span>
            </div>
            <div class="separator"></div>
            <div class="menu-item">
                <span><i class="fas fa-comment"></i>切换注释</span>
                <span class="shortcut">Ctrl+/</span>
            </div>
            <div class="menu-item">
                <span><i class="fas fa-align-left"></i>格式化文档</span>
                <span class="shortcut">Shift+Alt+F</span>
            </div>
        </div>

        <h2>详细测试步骤</h2>

        <div class="test-step">
            <h3>步骤 1: 基础右键菜单测试</h3>
            <p>1. 打开VS Code编辑器</p>
            <p>2. 在代码编辑区域右键点击</p>
            <p>3. 确认右键菜单正常显示</p>
            <p>4. 检查所有菜单项是否完整</p>
        </div>

        <div class="test-step">
            <h3>步骤 2: 测试基础编辑功能</h3>
            <p>1. 在编辑器中输入一些文本</p>
            <p>2. 选中部分文本，右键选择"复制"</p>
            <p>3. 移动光标到其他位置，右键选择"粘贴"</p>
            <p>4. 选中文本，右键选择"剪切"</p>
            <p>5. 测试"撤销"和"重做"功能</p>
        </div>

        <div class="test-step">
            <h3>步骤 3: 测试选择功能</h3>
            <p>1. 右键选择"全选"，确认所有文本被选中</p>
            <p>2. 将光标放在某一行，右键选择"选择行"</p>
            <p>3. 确认整行被选中</p>
        </div>

        <div class="test-step">
            <h3>步骤 4: 测试查找替换功能</h3>
            <p>1. 右键选择"查找"，确认搜索面板打开</p>
            <p>2. 右键选择"替换"，确认替换输入框获得焦点</p>
            <p>3. 右键选择"转到行"，输入行号测试跳转</p>
        </div>

        <div class="test-step">
            <h3>步骤 5: 测试代码工具功能</h3>
            <p>1. 输入一些代码，选中几行</p>
            <p>2. 右键选择"切换注释"，确认注释被添加/移除</p>
            <p>3. 右键选择"格式化文档"，确认代码被格式化</p>
            <p>4. 测试键盘快捷键 Ctrl+/ 和 Shift+Alt+F</p>
        </div>

        <div class="test-step">
            <h3>步骤 6: 测试智能启用/禁用</h3>
            <p>1. 在没有选中文本时右键，确认"剪切"和"复制"被禁用</p>
            <p>2. 选中文本后右键，确认"剪切"和"复制"被启用</p>
            <p>3. 在空编辑器中右键，确认"全选"被禁用</p>
            <p>4. 有内容时确认"全选"被启用</p>
        </div>

        <div class="test-step">
            <h3>步骤 7: 测试键盘快捷键集成</h3>
            <p>1. 使用 Ctrl+Z 测试撤销</p>
            <p>2. 使用 Ctrl+Y 测试重做</p>
            <p>3. 使用 Ctrl+X/C/V 测试剪切/复制/粘贴</p>
            <p>4. 使用 Ctrl+A 测试全选</p>
            <p>5. 使用 Ctrl+L 测试选择行</p>
        </div>

        <div class="test-step success">
            <h3>预期结果</h3>
            <p>✅ 右键菜单正常显示和隐藏</p>
            <p>✅ 所有编辑功能正常工作</p>
            <p>✅ 菜单项根据上下文智能启用/禁用</p>
            <p>✅ 键盘快捷键与菜单功能一致</p>
            <p>✅ 剪贴板操作正常工作</p>
            <p>✅ 代码工具功能正常</p>
        </div>

        <div class="test-step warning">
            <h3>注意事项</h3>
            <p>⚠️ 某些浏览器可能限制剪贴板访问，需要用户授权</p>
            <p>⚠️ 格式化功能是基础实现，复杂代码可能需要手动调整</p>
            <p>⚠️ 撤销/重做功能基于简单的历史记录</p>
            <p>⚠️ 如果遇到问题，请检查浏览器控制台的错误信息</p>
        </div>

        <h2>技术实现亮点</h2>
        <ul>
            <li><strong>现代剪贴板API</strong>：使用Clipboard API替代已弃用的document.execCommand</li>
            <li><strong>智能状态管理</strong>：根据编辑器状态动态启用/禁用菜单项</li>
            <li><strong>键盘快捷键集成</strong>：右键菜单功能与键盘快捷键完全一致</li>
            <li><strong>代码感知功能</strong>：支持注释切换和基础代码格式化</li>
            <li><strong>用户体验优化</strong>：符合现代编辑器的交互习惯</li>
        </ul>
    </div>
</body>
</html>
