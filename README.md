# VS Code 风格编辑器

一个使用 HTML、CSS 和 JavaScript 构建的 VS Code 风格的代码编辑器。

## 功能特性

### 🎨 界面设计
- **VS Code 深色主题**：完全模仿 VS Code 的视觉风格
- **经典布局**：包含顶部菜单栏、活动栏、侧边栏、编辑器区域和状态栏
- **下拉菜单**：完整的菜单栏功能，包含文件、编辑、查看等菜单
- **响应式设计**：支持 PC 和移动设备，自适应不同屏幕尺寸

### 📁 文件管理
- **文件资源管理器**：树形结构显示文件列表
- **文件操作**：支持打开、创建、删除文件
- **文件图标**：根据文件类型显示相应图标
- **标签页管理**：多文件标签页切换，支持关闭标签页

### ✏️ 代码编辑
- **代码编辑器**：支持多行文本编辑
- **行号显示**：自动生成和同步行号
- **语法高亮**：基础的语法高亮支持（可扩展）
- **自动保存**：编辑时自动保存文件内容

### 🔍 搜索功能
- **文件内搜索**：支持在当前文件中搜索文本
- **搜索面板**：专门的搜索界面
- **替换功能**：支持文本替换（界面已准备）

### 🍽️ 菜单功能
- **文件菜单**：新建、打开、保存、关闭文件
- **编辑菜单**：撤销、重做、剪切、复制、粘贴、查找、替换
- **选择菜单**：全选、选择行等选择操作
- **查看菜单**：切换侧边栏、缩放控制
- **转到菜单**：转到行、转到文件等导航功能
- **运行菜单**：运行当前文件
- **帮助菜单**：关于信息、快捷键说明

### ⌨️ 键盘快捷键
- **Ctrl+N**：创建新文件
- **Ctrl+O**：打开文件
- **Ctrl+S**：保存当前文件
- **Ctrl+W**：关闭当前标签页
- **Ctrl+F**：打开搜索面板
- **Ctrl+H**：打开替换功能
- **Ctrl+B**：切换侧边栏显示
- **Ctrl+G**：转到指定行
- **Ctrl+P**：转到文件
- **Ctrl+L**：选择当前行
- **Ctrl++**：放大编辑器
- **Ctrl+-**：缩小编辑器
- **Ctrl+0**：重置缩放
- **F5**：运行当前文件
- **Esc**：关闭菜单/侧边栏

### 📱 移动端支持
- **自适应布局**：在移动设备上优化显示
- **侧边栏控制**：移动端可通过点击活动栏切换侧边栏
- **触摸友好**：优化触摸操作体验

## 文件结构

```
├── index.html          # 主页面文件
├── styles.css          # 样式文件
├── script.js           # JavaScript 功能文件
├── test.html           # 功能测试页面
└── README.md           # 说明文档
```

## 使用方法

### 基本操作

1. **打开编辑器**：在浏览器中打开 `index.html`
2. **使用菜单**：点击顶部菜单栏项目查看下拉菜单选项
3. **切换面板**：点击左侧活动栏的图标切换不同功能面板
4. **打开文件**：在文件资源管理器中点击文件名或使用菜单
5. **编辑代码**：在右侧编辑器区域编写代码
6. **切换文件**：点击顶部标签页在不同文件间切换

### 文件操作

- **新建文件**：点击文件资源管理器标题栏的 "+" 图标
- **关闭文件**：点击标签页的 "×" 图标
- **搜索文本**：使用 Ctrl+F 或点击搜索面板

### 移动端使用

- 在移动设备上，侧边栏默认隐藏
- 点击活动栏图标可打开/关闭侧边栏
- 点击遮罩层或按 Esc 键关闭侧边栏

## 技术实现

### HTML 结构
- 语义化的 HTML5 结构
- 模块化的组件设计
- 无障碍访问支持

### CSS 样式
- Flexbox 布局
- CSS Grid（部分区域）
- 媒体查询实现响应式设计
- CSS 变量管理主题色彩
- 自定义滚动条样式

### JavaScript 功能
- ES6+ 语法
- 面向对象编程
- 事件驱动架构
- 模块化代码组织

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 扩展功能

编辑器设计为可扩展的架构，可以轻松添加以下功能：

- **语法高亮**：集成 Prism.js 或 CodeMirror
- **代码补全**：添加智能代码提示
- **主题切换**：支持多种颜色主题
- **插件系统**：模块化插件架构
- **Git 集成**：版本控制功能
- **终端集成**：内置终端面板
- **文件上传**：支持本地文件导入

## 开发说明

### 自定义样式
编辑 `styles.css` 文件可以自定义编辑器的外观：
- 修改 CSS 变量调整主题色彩
- 调整布局尺寸和间距
- 添加新的组件样式

### 添加功能
在 `script.js` 中的 `VSCodeEditor` 类中添加新方法：
- 扩展文件操作功能
- 添加新的键盘快捷键
- 实现更复杂的编辑功能

### 响应式调整
在 `styles.css` 的媒体查询部分调整移动端显示：
- 修改断点值
- 调整移动端布局
- 优化触摸交互

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个编辑器！
