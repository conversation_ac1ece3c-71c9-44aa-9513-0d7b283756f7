// VS Code 风格编辑器 JavaScript 功能

class VSCodeEditor {
    constructor() {
        this.currentFile = 'index.html';
        this.files = {
            'index.html': '<!DOCTYPE html>\n<html lang="zh-CN">\n<head>\n    <meta charset="UTF-8">\n    <meta name="viewport" content="width=device-width, initial-scale=1.0">\n    <title>VS Code Editor</title>\n</head>\n<body>\n    <h1>Hello World!</h1>\n</body>\n</html>',
            'styles.css': '/* CSS 样式 */\nbody {\n    font-family: Arial, sans-serif;\n    margin: 0;\n    padding: 20px;\n}\n\nh1 {\n    color: #333;\n    text-align: center;\n}',
            'script.js': '// JavaScript 代码\nconsole.log("Hello World!");\n\nfunction greet(name) {\n    return `Hello, ${name}!`;\n}\n\ngreet("VS Code Editor");',
            'README.md': '# VS Code Editor\n\n这是一个模仿 VS Code 风格的编辑器。\n\n## 功能特性\n\n- 文件管理\n- 代码编辑\n- 语法高亮\n- 标签页切换\n- 响应式设计'
        };
        this.openTabs = ['index.html', 'styles.css'];
        this.isMobile = window.innerWidth <= 768;
        this.sidebarOpen = false;
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.updateLineNumbers();
        this.loadFile(this.currentFile);
    }

    setupEventListeners() {
        // 活动栏切换
        document.querySelectorAll('.activity-item').forEach(item => {
            item.addEventListener('click', (e) => {
                this.switchPanel(e.currentTarget.dataset.panel);
                if (this.isMobile) {
                    this.toggleSidebar();
                }
            });
        });

        // 文件点击
        document.querySelectorAll('.file-item').forEach(item => {
            item.addEventListener('click', (e) => {
                const fileName = e.currentTarget.dataset.file;
                this.openFile(fileName);
            });
        });

        // 标签页切换
        document.querySelectorAll('.tab').forEach(tab => {
            tab.addEventListener('click', (e) => {
                if (!e.target.classList.contains('tab-close')) {
                    const fileName = e.currentTarget.dataset.file;
                    this.switchTab(fileName);
                }
            });
        });

        // 标签页关闭
        document.querySelectorAll('.tab-close').forEach(closeBtn => {
            closeBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                const fileName = e.currentTarget.closest('.tab').dataset.file;
                this.closeTab(fileName);
            });
        });

        // 代码编辑器
        const codeEditor = document.querySelector('.code-editor');
        codeEditor.addEventListener('input', () => {
            this.updateLineNumbers();
            this.saveCurrentFile();
        });

        codeEditor.addEventListener('scroll', () => {
            this.syncLineNumbers();
        });

        // 新建文件
        document.querySelector('.fa-file-plus').addEventListener('click', () => {
            this.createNewFile();
        });

        // 搜索功能
        const searchInput = document.querySelector('.search-input');
        searchInput.addEventListener('input', (e) => {
            this.searchInCode(e.target.value);
        });

        // 窗口控制
        document.querySelector('.control.minimize').addEventListener('click', () => {
            console.log('最小化窗口');
        });

        document.querySelector('.control.maximize').addEventListener('click', () => {
            console.log('最大化窗口');
        });

        document.querySelector('.control.close').addEventListener('click', () => {
            console.log('关闭窗口');
        });

        // 侧边栏遮罩点击
        document.querySelector('.sidebar-overlay').addEventListener('click', () => {
            this.closeSidebar();
        });

        // 窗口大小改变
        window.addEventListener('resize', () => {
            this.isMobile = window.innerWidth <= 768;
            if (!this.isMobile) {
                this.closeSidebar();
            }
        });

        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            this.handleKeyboardShortcuts(e);
        });
    }

    switchPanel(panelName) {
        // 更新活动栏状态
        document.querySelectorAll('.activity-item').forEach(item => {
            item.classList.remove('active');
        });
        document.querySelector(`[data-panel="${panelName}"]`).classList.add('active');

        // 切换面板
        document.querySelectorAll('.panel').forEach(panel => {
            panel.classList.remove('active');
        });
        document.querySelector(`.${panelName}-panel`).classList.add('active');
    }

    openFile(fileName) {
        if (!this.openTabs.includes(fileName)) {
            this.openTabs.push(fileName);
            this.createTab(fileName);
        }
        this.switchTab(fileName);
    }

    createTab(fileName) {
        const tabBar = document.querySelector('.tab-bar');
        const tab = document.createElement('div');
        tab.className = 'tab';
        tab.dataset.file = fileName;
        
        const fileIcon = this.getFileIcon(fileName);
        tab.innerHTML = `
            <i class="${fileIcon}"></i>
            <span class="tab-name">${fileName}</span>
            <i class="fas fa-times tab-close"></i>
        `;

        // 添加事件监听器
        tab.addEventListener('click', (e) => {
            if (!e.target.classList.contains('tab-close')) {
                this.switchTab(fileName);
            }
        });

        tab.querySelector('.tab-close').addEventListener('click', (e) => {
            e.stopPropagation();
            this.closeTab(fileName);
        });

        tabBar.appendChild(tab);
    }

    switchTab(fileName) {
        // 保存当前文件
        this.saveCurrentFile();

        // 更新当前文件
        this.currentFile = fileName;

        // 更新标签页状态
        document.querySelectorAll('.tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-file="${fileName}"]`).classList.add('active');

        // 更新文件列表状态
        document.querySelectorAll('.file-item').forEach(item => {
            item.classList.remove('active');
        });
        document.querySelector(`.file-item[data-file="${fileName}"]`).classList.add('active');

        // 加载文件内容
        this.loadFile(fileName);
    }

    closeTab(fileName) {
        const tabIndex = this.openTabs.indexOf(fileName);
        if (tabIndex > -1) {
            this.openTabs.splice(tabIndex, 1);
            document.querySelector(`[data-file="${fileName}"]`).remove();

            // 如果关闭的是当前标签页，切换到其他标签页
            if (fileName === this.currentFile && this.openTabs.length > 0) {
                this.switchTab(this.openTabs[this.openTabs.length - 1]);
            } else if (this.openTabs.length === 0) {
                this.currentFile = null;
                document.querySelector('.code-editor').value = '';
                this.updateLineNumbers();
            }
        }
    }

    loadFile(fileName) {
        const codeEditor = document.querySelector('.code-editor');
        codeEditor.value = this.files[fileName] || '';
        this.updateLineNumbers();
        this.updateStatusBar(fileName);
    }

    saveCurrentFile() {
        if (this.currentFile) {
            const codeEditor = document.querySelector('.code-editor');
            this.files[this.currentFile] = codeEditor.value;
        }
    }

    updateLineNumbers() {
        const codeEditor = document.querySelector('.code-editor');
        const lineNumbers = document.querySelector('.line-numbers');
        const lines = codeEditor.value.split('\n');
        
        lineNumbers.innerHTML = '';
        for (let i = 1; i <= Math.max(lines.length, 10); i++) {
            const lineNumber = document.createElement('div');
            lineNumber.className = 'line-number';
            lineNumber.textContent = i;
            lineNumbers.appendChild(lineNumber);
        }
    }

    syncLineNumbers() {
        const codeEditor = document.querySelector('.code-editor');
        const lineNumbers = document.querySelector('.line-numbers');
        lineNumbers.scrollTop = codeEditor.scrollTop;
    }

    updateStatusBar(fileName) {
        const fileType = this.getFileType(fileName);
        const statusRight = document.querySelector('.status-right');
        const fileTypeElement = statusRight.children[2];
        fileTypeElement.textContent = fileType;
    }

    getFileIcon(fileName) {
        const extension = fileName.split('.').pop().toLowerCase();
        const iconMap = {
            'html': 'fab fa-html5',
            'css': 'fab fa-css3-alt',
            'js': 'fab fa-js-square',
            'md': 'fab fa-markdown',
            'json': 'fas fa-code',
            'txt': 'fas fa-file-alt'
        };
        return iconMap[extension] || 'fas fa-file';
    }

    getFileType(fileName) {
        const extension = fileName.split('.').pop().toLowerCase();
        const typeMap = {
            'html': 'HTML',
            'css': 'CSS',
            'js': 'JavaScript',
            'md': 'Markdown',
            'json': 'JSON',
            'txt': 'Plain Text'
        };
        return typeMap[extension] || 'Unknown';
    }

    createNewFile() {
        const fileName = prompt('请输入文件名:');
        if (fileName && !this.files[fileName]) {
            this.files[fileName] = '';
            
            // 添加到文件列表
            const fileList = document.querySelector('.file-list');
            const fileItem = document.createElement('div');
            fileItem.className = 'file-item';
            fileItem.dataset.file = fileName;
            
            const fileIcon = this.getFileIcon(fileName);
            fileItem.innerHTML = `
                <i class="${fileIcon}"></i>
                <span>${fileName}</span>
            `;
            
            fileItem.addEventListener('click', () => {
                this.openFile(fileName);
            });
            
            fileList.appendChild(fileItem);
            this.openFile(fileName);
        }
    }

    searchInCode(searchTerm) {
        const codeEditor = document.querySelector('.code-editor');
        const content = codeEditor.value;

        if (searchTerm) {
            // 简单的搜索高亮（实际项目中可以使用更复杂的实现）
            const regex = new RegExp(searchTerm, 'gi');
            const matches = content.match(regex);
            console.log(`找到 ${matches ? matches.length : 0} 个匹配项`);
        }
    }

    toggleSidebar() {
        const sidebar = document.querySelector('.sidebar');
        const overlay = document.querySelector('.sidebar-overlay');

        if (this.sidebarOpen) {
            this.closeSidebar();
        } else {
            this.openSidebar();
        }
    }

    openSidebar() {
        const sidebar = document.querySelector('.sidebar');
        const overlay = document.querySelector('.sidebar-overlay');

        sidebar.classList.add('mobile-open');
        overlay.classList.add('active');
        this.sidebarOpen = true;
    }

    closeSidebar() {
        const sidebar = document.querySelector('.sidebar');
        const overlay = document.querySelector('.sidebar-overlay');

        sidebar.classList.remove('mobile-open');
        overlay.classList.remove('active');
        this.sidebarOpen = false;
    }

    handleKeyboardShortcuts(e) {
        // Ctrl+S 保存
        if (e.ctrlKey && e.key === 's') {
            e.preventDefault();
            this.saveCurrentFile();
            console.log('文件已保存');
        }

        // Ctrl+N 新建文件
        if (e.ctrlKey && e.key === 'n') {
            e.preventDefault();
            this.createNewFile();
        }

        // Ctrl+W 关闭标签页
        if (e.ctrlKey && e.key === 'w') {
            e.preventDefault();
            if (this.currentFile) {
                this.closeTab(this.currentFile);
            }
        }

        // Ctrl+F 搜索
        if (e.ctrlKey && e.key === 'f') {
            e.preventDefault();
            this.switchPanel('search');
            document.querySelector('.search-input').focus();
        }

        // Esc 关闭侧边栏（移动端）
        if (e.key === 'Escape' && this.isMobile && this.sidebarOpen) {
            this.closeSidebar();
        }
    }

    // 简单的语法高亮
    applySyntaxHighlighting() {
        const codeEditor = document.querySelector('.code-editor');
        const content = codeEditor.value;

        // 这里可以实现更复杂的语法高亮逻辑
        // 目前只是一个示例
        console.log('应用语法高亮');
    }
}

// 初始化编辑器
document.addEventListener('DOMContentLoaded', () => {
    new VSCodeEditor();
});
