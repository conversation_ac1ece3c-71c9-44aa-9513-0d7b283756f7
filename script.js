// VS Code 风格编辑器 JavaScript 功能

class VSCodeEditor {
    constructor() {
        this.currentFile = 'welcome';
        this.files = {
            'index.html': '<!DOCTYPE html>\n<html lang="zh-CN">\n<head>\n    <meta charset="UTF-8">\n    <meta name="viewport" content="width=device-width, initial-scale=1.0">\n    <title>VS Code Editor</title>\n</head>\n<body>\n    <h1>Hello World!</h1>\n</body>\n</html>',
            'styles.css': '/* CSS 样式 */\nbody {\n    font-family: Arial, sans-serif;\n    margin: 0;\n    padding: 20px;\n}\n\nh1 {\n    color: #333;\n    text-align: center;\n}',
            'script.js': '// JavaScript 代码\nconsole.log("Hello World!");\n\nfunction greet(name) {\n    return `Hello, ${name}!`;\n}\n\ngreet("VS Code Editor");',
            'README.md': '# VS Code Editor\n\n这是一个模仿 VS Code 风格的编辑器。\n\n## 功能特性\n\n- 文件管理\n- 代码编辑\n- 语法高亮\n- 标签页切换\n- 响应式设计',
            'main.css': '/* 主样式文件 */\n.container {\n    max-width: 1200px;\n    margin: 0 auto;\n    padding: 20px;\n}\n\n.header {\n    background: #333;\n    color: white;\n    padding: 1rem;\n}',
            'reset.css': '/* CSS Reset */\n* {\n    margin: 0;\n    padding: 0;\n    box-sizing: border-box;\n}\n\nbody {\n    font-family: Arial, sans-serif;\n}',
            'utils.js': '// 工具函数\nfunction formatDate(date) {\n    return date.toLocaleDateString();\n}\n\nfunction debounce(func, wait) {\n    let timeout;\n    return function executedFunction(...args) {\n        const later = () => {\n            clearTimeout(timeout);\n            func(...args);\n        };\n        clearTimeout(timeout);\n        timeout = setTimeout(later, wait);\n    };\n}',
            'config.js': '// 配置文件\nconst config = {\n    apiUrl: "https://api.example.com",\n    timeout: 5000,\n    retries: 3,\n    debug: true\n};\n\nexport default config;',
            'package.json': '{\n  "name": "vscode-editor",\n  "version": "1.0.0",\n  "description": "A VS Code style editor",\n  "main": "index.html",\n  "scripts": {\n    "start": "live-server",\n    "build": "webpack --mode production"\n  },\n  "keywords": ["editor", "vscode", "html", "css", "javascript"],\n  "author": "Developer",\n  "license": "MIT"\n}',
            'CHANGELOG.md': '# 更新日志\n\n## [1.0.0] - 2024-01-01\n\n### 新增\n- 初始版本发布\n- VS Code 风格界面\n- 基础编辑功能\n- 文件管理\n- 响应式设计\n\n### 修复\n- 修复了一些样式问题\n- 优化了性能'
        };
        this.openTabs = ['welcome']; // 默认打开欢迎标签页
        this.isMobile = window.innerWidth <= 768;
        this.sidebarOpen = false;
        this.undoStack = [];
        this.redoStack = [];
        this.recentFiles = this.loadRecentFiles();
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.updateLineNumbers();
        this.updateRecentFiles();
        this.setupWelcomePage();

        // 显示当前标签页（默认是欢迎页面）
        this.showTab(this.currentFile);
    }

    setupEventListeners() {
        // 活动栏切换
        document.querySelectorAll('.activity-item').forEach(item => {
            item.addEventListener('click', (e) => {
                this.switchPanel(e.currentTarget.dataset.panel);
                if (this.isMobile) {
                    this.toggleSidebar();
                }
            });
        });

        // 文件树点击事件
        this.setupFileTreeEvents();

        // 标签页事件（使用事件委托）
        this.setupTabEvents();

        // 代码编辑器事件将在创建编辑器标签页时动态添加

        // 新建文件
        document.querySelector('.fa-file-plus').addEventListener('click', () => {
            this.createNewFile();
        });

        // 搜索功能
        const searchInput = document.querySelector('.search-input');
        searchInput.addEventListener('input', (e) => {
            this.searchInCode(e.target.value);
        });

        // 窗口控制
        document.querySelector('.control.minimize').addEventListener('click', () => {
            console.log('最小化窗口');
        });

        document.querySelector('.control.maximize').addEventListener('click', () => {
            console.log('最大化窗口');
        });

        document.querySelector('.control.close').addEventListener('click', () => {
            console.log('关闭窗口');
        });

        // 侧边栏遮罩点击
        document.querySelector('.sidebar-overlay').addEventListener('click', () => {
            this.closeSidebar();
        });

        // 窗口大小改变
        window.addEventListener('resize', () => {
            this.isMobile = window.innerWidth <= 768;
            if (!this.isMobile) {
                this.closeSidebar();
            }
        });

        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            this.handleKeyboardShortcuts(e);
        });

        // 菜单项点击
        document.querySelectorAll('.menu-item').forEach(item => {
            item.addEventListener('click', (e) => {
                e.stopPropagation();
                this.toggleMenu(item);
            });
        });

        // 菜单选项点击
        document.querySelectorAll('.menu-option').forEach(option => {
            option.addEventListener('click', (e) => {
                e.stopPropagation();
                const action = option.dataset.action;
                this.handleMenuAction(action);
                this.closeAllMenus();
            });
        });

        // 点击其他地方关闭菜单
        document.addEventListener('click', () => {
            this.closeAllMenus();
            this.hideContextMenu();
            this.hideTabContextMenu();
            this.hideEditorContextMenu();
        });

        // 右键菜单事件
        this.setupContextMenu();
        this.setupEditorContextMenu();
    }

    // 设置欢迎页面
    setupWelcomePage() {
        // 欢迎页面操作按钮事件
        document.querySelectorAll('.action-item').forEach(item => {
            item.addEventListener('click', () => {
                const action = item.dataset.action;
                this.handleWelcomeAction(action);
            });
        });

        // 最近文件点击事件
        document.querySelectorAll('.recent-item').forEach(item => {
            item.addEventListener('click', () => {
                const fileName = item.dataset.file;
                if (fileName && this.files[fileName]) {
                    this.openFile(fileName);
                }
            });
        });

        // 最近文件操作按钮
        document.querySelectorAll('.recent-action-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                const action = btn.dataset.action;
                this.handleRecentAction(action);
            });
        });

        // 帮助链接事件
        document.querySelectorAll('.help-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const action = link.dataset.action;
                this.handleHelpAction(action);
            });
        });

        // 底部链接事件
        document.querySelectorAll('.welcome-footer a').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const action = link.dataset.action;
                this.handleFooterAction(action);
            });
        });
    }

    // 显示指定标签页
    showTab(fileName) {
        // 隐藏所有标签页内容
        document.querySelectorAll('.editor-tab').forEach(tab => {
            tab.classList.remove('active');
        });

        // 显示指定标签页
        if (fileName === 'welcome') {
            document.getElementById('welcomePage').classList.add('active');
        } else {
            const editorTab = document.querySelector(`[data-file="${fileName}"].editor-tab`);
            if (editorTab) {
                editorTab.classList.add('active');
            }
        }
    }

    // 创建代码编辑器标签页
    createEditorTab(fileName) {
        const editorContent = document.querySelector('.editor-content');
        const editorTab = document.createElement('div');
        editorTab.className = 'editor-tab code-editor-container';
        editorTab.dataset.file = fileName;

        editorTab.innerHTML = `
            <div class="line-numbers">
                <div class="line-number">1</div>
            </div>
            <textarea class="code-editor" placeholder="开始编写代码..."></textarea>
        `;

        editorContent.appendChild(editorTab);

        // 为新的编辑器添加事件监听器
        const codeEditor = editorTab.querySelector('.code-editor');
        codeEditor.addEventListener('input', () => {
            this.updateLineNumbers(editorTab);
            this.saveCurrentFile();
        });

        codeEditor.addEventListener('scroll', () => {
            this.syncLineNumbers(editorTab);
        });

        codeEditor.addEventListener('contextmenu', (e) => {
            e.preventDefault();
            this.showEditorContextMenu(e.clientX, e.clientY);
        });

        return editorTab;
    }

    // 处理欢迎页面操作
    handleWelcomeAction(action) {
        switch (action) {
            case 'new-file':
                this.createNewFile();
                break;
            case 'open-file':
                this.openFileDialog();
                break;
            case 'open-folder':
                this.openFolderDialog();
                break;
            case 'clone-repo':
                this.cloneRepository();
                break;
            default:
                console.log(`未实现的欢迎页面操作: ${action}`);
        }
    }

    // 处理最近文件操作
    handleRecentAction(action) {
        switch (action) {
            case 'clear-recent':
                this.clearRecentFiles();
                break;
            case 'more-recent':
                this.showMoreRecentFiles();
                break;
            default:
                console.log(`未实现的最近文件操作: ${action}`);
        }
    }

    // 处理帮助操作
    handleHelpAction(action) {
        switch (action) {
            case 'show-shortcuts':
                this.showShortcuts();
                break;
            case 'show-docs':
                this.showDocumentation();
                break;
            case 'show-tips':
                this.showTipsAndTricks();
                break;
            case 'show-about':
                this.showAbout();
                break;
            default:
                console.log(`未实现的帮助操作: ${action}`);
        }
    }

    // 处理底部链接操作
    handleFooterAction(action) {
        switch (action) {
            case 'show-changelog':
                this.showChangelog();
                break;
            case 'show-settings':
                this.showSettings();
                break;
            default:
                console.log(`未实现的底部操作: ${action}`);
        }
    }

    switchPanel(panelName) {
        // 更新活动栏状态
        document.querySelectorAll('.activity-item').forEach(item => {
            item.classList.remove('active');
        });
        document.querySelector(`[data-panel="${panelName}"]`).classList.add('active');

        // 切换面板
        document.querySelectorAll('.panel').forEach(panel => {
            panel.classList.remove('active');
        });
        document.querySelector(`.${panelName}-panel`).classList.add('active');
    }

    openFile(fileName) {
        if (!this.openTabs.includes(fileName)) {
            this.openTabs.push(fileName);
            this.createTab(fileName);

            // 如果不是欢迎页面，创建编辑器标签页
            if (fileName !== 'welcome') {
                this.createEditorTab(fileName);
            }
        }
        this.switchTab(fileName);

        // 添加到最近文件列表
        if (fileName !== 'welcome') {
            this.addToRecentFiles(fileName);
        }
    }

    createTab(fileName) {
        const tabBar = document.querySelector('.tab-bar');
        const tab = document.createElement('div');
        tab.className = 'tab';
        tab.dataset.file = fileName;
        
        const fileIcon = this.getFileIcon(fileName);
        tab.innerHTML = `
            <i class="${fileIcon}"></i>
            <span class="tab-name">${fileName}</span>
            <i class="fas fa-times tab-close"></i>
        `;

        tabBar.appendChild(tab);
    }

    switchTab(fileName) {
        // 保存当前文件
        this.saveCurrentFile();

        // 更新当前文件
        this.currentFile = fileName;

        // 更新标签页状态
        document.querySelectorAll('.tab-bar .tab').forEach(tab => {
            tab.classList.remove('active');
        });
        const activeTab = document.querySelector(`.tab-bar .tab[data-file="${fileName}"]`);
        if (activeTab) {
            activeTab.classList.add('active');
        }

        // 显示对应的标签页内容
        this.showTab(fileName);

        // 如果不是欢迎页面，高亮文件在树中的位置并加载文件内容
        if (fileName !== 'welcome') {
            this.highlightFileInTree(fileName);
            this.loadFile(fileName);
        }
    }

    closeTab(fileName) {
        // 不允许关闭欢迎标签页，如果只剩欢迎标签页
        if (fileName === 'welcome' && this.openTabs.length === 1) {
            return;
        }

        const tabIndex = this.openTabs.indexOf(fileName);
        if (tabIndex > -1) {
            this.openTabs.splice(tabIndex, 1);

            // 删除标签栏中的标签页
            const tabToRemove = document.querySelector(`.tab-bar .tab[data-file="${fileName}"]`);
            if (tabToRemove) {
                tabToRemove.remove();
            }

            // 删除编辑器标签页内容（如果不是欢迎页面）
            if (fileName !== 'welcome') {
                const editorTabToRemove = document.querySelector(`[data-file="${fileName}"].editor-tab`);
                if (editorTabToRemove) {
                    editorTabToRemove.remove();
                }
            }

            // 如果关闭的是当前标签页，切换到其他标签页
            if (fileName === this.currentFile && this.openTabs.length > 0) {
                this.switchTab(this.openTabs[this.openTabs.length - 1]);
            } else if (this.openTabs.length === 0) {
                // 如果没有标签页了，重新打开欢迎页面
                this.openTabs = ['welcome'];
                this.currentFile = 'welcome';
                this.createTab('welcome');
                this.showTab('welcome');
            }
        }
    }

    loadFile(fileName) {
        if (fileName === 'welcome') return;

        const editorTab = document.querySelector(`[data-file="${fileName}"].editor-tab`);
        if (editorTab) {
            const codeEditor = editorTab.querySelector('.code-editor');
            codeEditor.value = this.files[fileName] || '';
            this.updateLineNumbers(editorTab);
            this.updateStatusBar(fileName);
        }
    }

    saveCurrentFile() {
        if (this.currentFile && this.currentFile !== 'welcome') {
            const editorTab = document.querySelector(`[data-file="${this.currentFile}"].editor-tab`);
            if (editorTab) {
                const codeEditor = editorTab.querySelector('.code-editor');
                if (codeEditor) {
                    this.files[this.currentFile] = codeEditor.value;
                }
            }
        }
    }

    updateLineNumbers(editorTab = null) {
        // 如果没有指定编辑器标签页，使用当前活动的编辑器
        if (!editorTab) {
            editorTab = document.querySelector(`[data-file="${this.currentFile}"].editor-tab`);
        }

        if (!editorTab) return;

        const codeEditor = editorTab.querySelector('.code-editor');
        const lineNumbers = editorTab.querySelector('.line-numbers');

        if (!codeEditor || !lineNumbers) return;

        const lines = codeEditor.value.split('\n');

        lineNumbers.innerHTML = '';
        for (let i = 1; i <= Math.max(lines.length, 10); i++) {
            const lineNumber = document.createElement('div');
            lineNumber.className = 'line-number';
            lineNumber.textContent = i;
            lineNumbers.appendChild(lineNumber);
        }
    }

    syncLineNumbers(editorTab = null) {
        // 如果没有指定编辑器标签页，使用当前活动的编辑器
        if (!editorTab) {
            editorTab = document.querySelector(`[data-file="${this.currentFile}"].editor-tab`);
        }

        if (!editorTab) return;

        const codeEditor = editorTab.querySelector('.code-editor');
        const lineNumbers = editorTab.querySelector('.line-numbers');

        if (codeEditor && lineNumbers) {
            lineNumbers.scrollTop = codeEditor.scrollTop;
        }
    }

    updateStatusBar(fileName) {
        const fileType = this.getFileType(fileName);
        const statusRight = document.querySelector('.status-right');
        const fileTypeElement = statusRight.children[2];
        fileTypeElement.textContent = fileType;
    }

    getFileIcon(fileName) {
        const extension = fileName.split('.').pop().toLowerCase();
        const iconMap = {
            'html': 'fab fa-html5',
            'css': 'fab fa-css3-alt',
            'js': 'fab fa-js-square',
            'md': 'fab fa-markdown',
            'json': 'fas fa-code',
            'txt': 'fas fa-file-alt'
        };
        return iconMap[extension] || 'fas fa-file';
    }

    getFileType(fileName) {
        const extension = fileName.split('.').pop().toLowerCase();
        const typeMap = {
            'html': 'HTML',
            'css': 'CSS',
            'js': 'JavaScript',
            'md': 'Markdown',
            'json': 'JSON',
            'txt': 'Plain Text'
        };
        return typeMap[extension] || 'Unknown';
    }

    createNewFile() {
        const fileName = prompt('请输入文件名:');
        if (fileName && !this.files[fileName]) {
            this.files[fileName] = '';
            
            // 添加到文件列表
            const fileList = document.querySelector('.file-list');
            const fileItem = document.createElement('div');
            fileItem.className = 'file-item';
            fileItem.dataset.file = fileName;
            
            const fileIcon = this.getFileIcon(fileName);
            fileItem.innerHTML = `
                <i class="${fileIcon}"></i>
                <span>${fileName}</span>
            `;
            
            fileItem.addEventListener('click', () => {
                this.openFile(fileName);
            });
            
            fileList.appendChild(fileItem);
            this.openFile(fileName);
        }
    }

    searchInCode(searchTerm) {
        const codeEditor = document.querySelector('.code-editor');
        const content = codeEditor.value;

        if (searchTerm) {
            // 简单的搜索高亮（实际项目中可以使用更复杂的实现）
            const regex = new RegExp(searchTerm, 'gi');
            const matches = content.match(regex);
            console.log(`找到 ${matches ? matches.length : 0} 个匹配项`);
        }
    }

    toggleSidebar() {
        const sidebar = document.querySelector('.sidebar');
        const overlay = document.querySelector('.sidebar-overlay');

        if (this.sidebarOpen) {
            this.closeSidebar();
        } else {
            this.openSidebar();
        }
    }

    openSidebar() {
        const sidebar = document.querySelector('.sidebar');
        const overlay = document.querySelector('.sidebar-overlay');

        sidebar.classList.add('mobile-open');
        overlay.classList.add('active');
        this.sidebarOpen = true;
    }

    closeSidebar() {
        const sidebar = document.querySelector('.sidebar');
        const overlay = document.querySelector('.sidebar-overlay');

        sidebar.classList.remove('mobile-open');
        overlay.classList.remove('active');
        this.sidebarOpen = false;
    }

    handleKeyboardShortcuts(e) {
        // 关闭菜单
        if (e.key === 'Escape') {
            this.closeAllMenus();
            if (this.isMobile && this.sidebarOpen) {
                this.closeSidebar();
            }
        }

        // Ctrl+S 保存
        if (e.ctrlKey && e.key === 's') {
            e.preventDefault();
            this.saveCurrentFile();
            console.log('文件已保存');
        }

        // Ctrl+N 新建文件
        if (e.ctrlKey && e.key === 'n') {
            e.preventDefault();
            this.createNewFile();
        }

        // Ctrl+O 打开文件
        if (e.ctrlKey && e.key === 'o') {
            e.preventDefault();
            this.openFileDialog();
        }

        // Ctrl+W 关闭标签页
        if (e.ctrlKey && e.key === 'w') {
            e.preventDefault();
            if (this.currentFile) {
                this.closeTab(this.currentFile);
            }
        }

        // Ctrl+F 搜索
        if (e.ctrlKey && e.key === 'f') {
            e.preventDefault();
            this.switchPanel('search');
            document.querySelector('.search-input').focus();
        }

        // Ctrl+H 替换
        if (e.ctrlKey && e.key === 'h') {
            e.preventDefault();
            this.switchPanel('search');
            document.querySelector('.replace-input').focus();
        }

        // Ctrl+B 切换侧边栏
        if (e.ctrlKey && e.key === 'b') {
            e.preventDefault();
            this.toggleSidebarVisibility();
        }

        // Ctrl+G 转到行
        if (e.ctrlKey && e.key === 'g') {
            e.preventDefault();
            this.goToLine();
        }

        // Ctrl+P 转到文件
        if (e.ctrlKey && e.key === 'p') {
            e.preventDefault();
            this.goToFile();
        }

        // Ctrl+L 选择行
        if (e.ctrlKey && e.key === 'l') {
            e.preventDefault();
            this.selectCurrentLine();
        }

        // 缩放快捷键
        if (e.ctrlKey && e.key === '=') {
            e.preventDefault();
            this.adjustZoom(1.1);
        }

        if (e.ctrlKey && e.key === '-') {
            e.preventDefault();
            this.adjustZoom(0.9);
        }

        if (e.ctrlKey && e.key === '0') {
            e.preventDefault();
            this.resetZoom();
        }

        // F5 运行文件
        if (e.key === 'F5') {
            e.preventDefault();
            this.runCurrentFile();
        }

        // Ctrl+/ 切换注释
        if (e.ctrlKey && e.key === '/') {
            e.preventDefault();
            this.toggleComment();
        }

        // Shift+Alt+F 格式化文档
        if (e.shiftKey && e.altKey && e.key === 'F') {
            e.preventDefault();
            this.formatDocument();
        }
    }

    // 简单的语法高亮
    applySyntaxHighlighting() {
        const codeEditor = document.querySelector('.code-editor');
        const content = codeEditor.value;

        // 这里可以实现更复杂的语法高亮逻辑
        // 目前只是一个示例
        console.log('应用语法高亮');
    }

    // 菜单控制
    toggleMenu(menuItem) {
        const isActive = menuItem.classList.contains('active');

        // 关闭所有菜单
        this.closeAllMenus();

        // 如果当前菜单未激活，则激活它
        if (!isActive) {
            menuItem.classList.add('active');
        }
    }

    closeAllMenus() {
        document.querySelectorAll('.menu-item').forEach(item => {
            item.classList.remove('active');
        });
    }

    // 处理菜单动作
    handleMenuAction(action) {
        switch (action) {
            case 'new-file':
                this.createNewFile();
                break;
            case 'open-file':
                this.openFileDialog();
                break;
            case 'save':
                this.saveCurrentFile();
                console.log('文件已保存');
                break;
            case 'save-as':
                this.saveAsFile();
                break;
            case 'close-file':
                if (this.currentFile) {
                    this.closeTab(this.currentFile);
                }
                break;
            case 'undo':
                document.execCommand('undo');
                break;
            case 'redo':
                document.execCommand('redo');
                break;
            case 'cut':
                document.execCommand('cut');
                break;
            case 'copy':
                document.execCommand('copy');
                break;
            case 'paste':
                document.execCommand('paste');
                break;
            case 'find':
                this.switchPanel('search');
                document.querySelector('.search-input').focus();
                break;
            case 'replace':
                this.switchPanel('search');
                document.querySelector('.replace-input').focus();
                break;
            case 'select-all':
                document.querySelector('.code-editor').select();
                break;
            case 'select-line':
                this.selectCurrentLine();
                break;
            case 'toggle-sidebar':
                this.toggleSidebarVisibility();
                break;
            case 'toggle-panel':
                console.log('切换面板');
                break;
            case 'zoom-in':
                this.adjustZoom(1.1);
                break;
            case 'zoom-out':
                this.adjustZoom(0.9);
                break;
            case 'reset-zoom':
                this.resetZoom();
                break;
            case 'go-to-line':
                this.goToLine();
                break;
            case 'go-to-file':
                this.goToFile();
                break;
            case 'run-file':
                this.runCurrentFile();
                break;
            case 'new-terminal':
                console.log('新建终端');
                break;
            case 'about':
                this.showAbout();
                break;
            case 'shortcuts':
                this.showShortcuts();
                break;
            default:
                console.log(`未实现的动作: ${action}`);
        }
    }

    // 新增的菜单功能方法
    openFileDialog() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.txt,.js,.html,.css,.md,.json';
        input.onchange = (e) => {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    this.files[file.name] = e.target.result;
                    this.addFileToExplorer(file.name);
                    this.openFile(file.name);
                };
                reader.readAsText(file);
            }
        };
        input.click();
    }

    saveAsFile() {
        if (this.currentFile) {
            const content = this.files[this.currentFile];
            const blob = new Blob([content], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = this.currentFile;
            a.click();
            URL.revokeObjectURL(url);
        }
    }

    selectCurrentLine() {
        const editor = document.querySelector('.code-editor');
        const start = editor.selectionStart;
        const value = editor.value;

        // 找到当前行的开始和结束
        let lineStart = value.lastIndexOf('\n', start - 1) + 1;
        let lineEnd = value.indexOf('\n', start);
        if (lineEnd === -1) lineEnd = value.length;

        editor.setSelectionRange(lineStart, lineEnd);
    }

    toggleSidebarVisibility() {
        const sidebar = document.querySelector('.sidebar');
        sidebar.style.display = sidebar.style.display === 'none' ? 'block' : 'none';
    }

    adjustZoom(factor) {
        const editor = document.querySelector('.code-editor');
        const currentSize = parseFloat(getComputedStyle(editor).fontSize);
        editor.style.fontSize = (currentSize * factor) + 'px';
    }

    resetZoom() {
        const editor = document.querySelector('.code-editor');
        editor.style.fontSize = '14px';
    }

    goToLine() {
        const lineNumber = prompt('转到行号:');
        if (lineNumber && !isNaN(lineNumber)) {
            const editor = document.querySelector('.code-editor');
            const lines = editor.value.split('\n');
            const targetLine = Math.min(Math.max(1, parseInt(lineNumber)), lines.length);

            // 计算目标位置
            let position = 0;
            for (let i = 0; i < targetLine - 1; i++) {
                position += lines[i].length + 1; // +1 for newline
            }

            editor.focus();
            editor.setSelectionRange(position, position);
        }
    }

    goToFile() {
        const fileName = prompt('输入文件名:');
        if (fileName && this.files[fileName]) {
            this.openFile(fileName);
        } else if (fileName) {
            alert('文件不存在');
        }
    }

    runCurrentFile() {
        if (this.currentFile && this.currentFile.endsWith('.html')) {
            const content = this.files[this.currentFile];
            const newWindow = window.open();
            newWindow.document.write(content);
        } else {
            console.log('运行当前文件');
        }
    }

    showAbout() {
        alert('VS Code 风格编辑器\n\n版本: 1.0.0\n使用 HTML + CSS + JavaScript 构建');
    }

    showShortcuts() {
        const shortcuts = `
键盘快捷键:

文件操作:
Ctrl+N - 新建文件
Ctrl+O - 打开文件
Ctrl+S - 保存文件
Ctrl+W - 关闭文件

编辑操作:
Ctrl+Z - 撤销
Ctrl+Y - 重做
Ctrl+X - 剪切
Ctrl+C - 复制
Ctrl+V - 粘贴
Ctrl+A - 全选
Ctrl+F - 查找
Ctrl+H - 替换

查看操作:
Ctrl+B - 切换侧边栏
Ctrl++ - 放大
Ctrl+- - 缩小
Ctrl+0 - 重置缩放

导航操作:
Ctrl+G - 转到行
Ctrl+P - 转到文件
        `;
        alert(shortcuts);
    }

    addFileToExplorer(fileName) {
        const fileTree = document.querySelector('.file-tree .tree-children');
        const existingFile = fileTree.querySelector(`[data-file="${fileName}"]`);

        if (!existingFile) {
            const fileItem = document.createElement('div');
            fileItem.className = 'tree-item file-item';
            fileItem.dataset.file = fileName;
            fileItem.dataset.path = `project/${fileName}`;

            const fileIcon = this.getFileIcon(fileName);
            fileItem.innerHTML = `
                <div class="tree-item-content">
                    <i class="${fileIcon} file-icon"></i>
                    <span class="item-name">${fileName}</span>
                </div>
            `;

            fileItem.addEventListener('click', (e) => {
                e.stopPropagation();
                this.selectTreeItem(fileItem);
                this.openFile(fileName);
            });

            fileTree.appendChild(fileItem);
        }
    }

    // 设置标签页事件
    setupTabEvents() {
        const tabBar = document.querySelector('.tab-bar');

        // 使用事件委托处理标签页点击
        tabBar.addEventListener('click', (e) => {
            const tab = e.target.closest('.tab');
            if (!tab) return;

            if (e.target.classList.contains('tab-close')) {
                // 点击关闭按钮
                e.stopPropagation();
                const fileName = tab.dataset.file;
                this.closeTab(fileName);
            } else {
                // 点击标签页其他区域
                const fileName = tab.dataset.file;
                this.switchTab(fileName);
            }
        });

        // 标签页右键菜单
        tabBar.addEventListener('contextmenu', (e) => {
            const tab = e.target.closest('.tab');
            if (tab) {
                e.preventDefault();
                this.showTabContextMenu(e.clientX, e.clientY, tab);
            }
        });

        // 设置标签页右键菜单事件
        this.setupTabContextMenu();
    }

    // 设置文件树事件
    setupFileTreeEvents() {
        // 为所有文件夹项添加展开/收缩事件
        document.querySelectorAll('.folder-item').forEach(folder => {
            const content = folder.querySelector('.tree-item-content');
            content.addEventListener('click', (e) => {
                e.stopPropagation();
                this.toggleFolder(folder);
            });
        });

        // 为所有文件项添加点击事件
        document.querySelectorAll('.file-item').forEach(file => {
            const content = file.querySelector('.tree-item-content');
            content.addEventListener('click', (e) => {
                e.stopPropagation();
                const fileName = file.dataset.file;
                if (fileName) {
                    this.selectTreeItem(file);
                    this.openFile(fileName);
                }
            });
        });
    }

    // 切换文件夹展开/收缩状态
    toggleFolder(folderElement) {
        const isExpanded = folderElement.dataset.expanded === 'true';
        const children = folderElement.querySelector('.tree-children');
        const expandIcon = folderElement.querySelector('.expand-icon');
        const folderIcon = folderElement.querySelector('.folder-icon');

        if (isExpanded) {
            // 收缩文件夹
            folderElement.dataset.expanded = 'false';
            children.style.display = 'none';
            expandIcon.className = 'fas fa-chevron-right expand-icon';
            folderIcon.className = 'fas fa-folder folder-icon';
        } else {
            // 展开文件夹
            folderElement.dataset.expanded = 'true';
            children.style.display = 'block';
            expandIcon.className = 'fas fa-chevron-down expand-icon';
            folderIcon.className = 'fas fa-folder-open folder-icon';
        }
    }

    // 选择树项目
    selectTreeItem(item) {
        // 移除所有选中状态
        document.querySelectorAll('.tree-item').forEach(treeItem => {
            treeItem.classList.remove('selected');
        });

        // 添加选中状态到当前项
        item.classList.add('selected');
    }

    // 展开到指定路径
    expandToPath(path) {
        const pathParts = path.split('/');
        let currentPath = '';

        for (let i = 0; i < pathParts.length - 1; i++) {
            currentPath += (i > 0 ? '/' : '') + pathParts[i];
            const folder = document.querySelector(`[data-path="${currentPath}"]`);
            if (folder && folder.classList.contains('folder-item')) {
                if (folder.dataset.expanded === 'false') {
                    this.toggleFolder(folder);
                }
            }
        }
    }

    // 查找文件在树中的位置
    findFileInTree(fileName) {
        return document.querySelector(`[data-file="${fileName}"]`);
    }

    // 高亮文件在树中的位置
    highlightFileInTree(fileName) {
        const fileItem = this.findFileInTree(fileName);
        if (fileItem) {
            // 展开到该文件的路径
            const path = fileItem.dataset.path;
            this.expandToPath(path);

            // 选中该文件
            this.selectTreeItem(fileItem);

            // 滚动到可见区域
            fileItem.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        }
    }

    // 获取文件夹内容
    getFolderContents(folderPath) {
        // 这里可以实现动态加载文件夹内容的逻辑
        // 目前返回静态内容
        return [];
    }

    // 创建新文件夹
    createNewFolder() {
        const folderName = prompt('请输入文件夹名称:');
        if (folderName) {
            // 这里可以实现创建新文件夹的逻辑
            console.log(`创建文件夹: ${folderName}`);
        }
    }

    // 重命名文件或文件夹
    renameItem(item) {
        const currentName = item.querySelector('.item-name').textContent;
        const newName = prompt('请输入新名称:', currentName);
        if (newName && newName !== currentName) {
            item.querySelector('.item-name').textContent = newName;
            // 这里可以实现重命名的逻辑
            console.log(`重命名: ${currentName} -> ${newName}`);
        }
    }

    // 删除文件或文件夹
    deleteItem(item) {
        const itemName = item.querySelector('.item-name').textContent;
        if (confirm(`确定要删除 "${itemName}" 吗？`)) {
            item.remove();
            // 这里可以实现删除的逻辑
            console.log(`删除: ${itemName}`);
        }
    }

    // 设置右键菜单
    setupContextMenu() {
        const contextMenu = document.getElementById('contextMenu');
        let currentContextItem = null;

        // 为文件树项添加右键事件
        document.addEventListener('contextmenu', (e) => {
            const treeItem = e.target.closest('.tree-item');
            if (treeItem && treeItem.closest('.file-tree')) {
                e.preventDefault();
                currentContextItem = treeItem;
                this.showContextMenu(e.clientX, e.clientY, treeItem);
            }
        });

        // 右键菜单项点击事件
        document.querySelectorAll('.context-menu-item').forEach(item => {
            item.addEventListener('click', (e) => {
                const action = item.dataset.action;
                this.handleContextMenuAction(action, currentContextItem);
                this.hideContextMenu();
            });
        });

        // 点击其他地方隐藏右键菜单
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.context-menu')) {
                this.hideContextMenu();
            }
        });
    }

    // 显示右键菜单
    showContextMenu(x, y, item) {
        const contextMenu = document.getElementById('contextMenu');
        const isFolder = item.classList.contains('folder-item');

        // 根据项目类型启用/禁用菜单项
        const deleteItem = contextMenu.querySelector('[data-action="delete"]');
        const renameItem = contextMenu.querySelector('[data-action="rename"]');

        // 设置菜单位置
        contextMenu.style.left = x + 'px';
        contextMenu.style.top = y + 'px';

        // 显示菜单
        contextMenu.classList.add('show');

        // 确保菜单在视窗内
        const rect = contextMenu.getBoundingClientRect();
        if (rect.right > window.innerWidth) {
            contextMenu.style.left = (x - rect.width) + 'px';
        }
        if (rect.bottom > window.innerHeight) {
            contextMenu.style.top = (y - rect.height) + 'px';
        }
    }

    // 隐藏右键菜单
    hideContextMenu() {
        const contextMenu = document.getElementById('contextMenu');
        contextMenu.classList.remove('show');
    }

    // 处理右键菜单动作
    handleContextMenuAction(action, item) {
        switch (action) {
            case 'new-file':
                this.createNewFileInFolder(item);
                break;
            case 'new-folder':
                this.createNewFolderInFolder(item);
                break;
            case 'rename':
                this.renameItem(item);
                break;
            case 'delete':
                this.deleteItem(item);
                break;
            case 'copy-path':
                this.copyItemPath(item);
                break;
            default:
                console.log(`未实现的右键菜单动作: ${action}`);
        }
    }

    // 在文件夹中创建新文件
    createNewFileInFolder(folderItem) {
        const fileName = prompt('请输入文件名:');
        if (fileName) {
            // 确保文件夹是展开的
            if (folderItem.classList.contains('folder-item') && folderItem.dataset.expanded === 'false') {
                this.toggleFolder(folderItem);
            }

            // 创建新文件项
            const newFileItem = document.createElement('div');
            newFileItem.className = 'tree-item file-item';
            newFileItem.dataset.file = fileName;
            newFileItem.dataset.path = `${folderItem.dataset.path}/${fileName}`;

            const fileIcon = this.getFileIcon(fileName);
            newFileItem.innerHTML = `
                <div class="tree-item-content">
                    <i class="${fileIcon} file-icon"></i>
                    <span class="item-name">${fileName}</span>
                </div>
            `;

            // 添加点击事件
            newFileItem.addEventListener('click', (e) => {
                e.stopPropagation();
                this.selectTreeItem(newFileItem);
                this.openFile(fileName);
            });

            // 添加到文件夹中
            const children = folderItem.querySelector('.tree-children');
            if (children) {
                children.appendChild(newFileItem);
            }

            // 创建空文件内容
            this.files[fileName] = '';

            console.log(`在 ${folderItem.dataset.path} 中创建文件: ${fileName}`);
        }
    }

    // 在文件夹中创建新文件夹
    createNewFolderInFolder(parentItem) {
        const folderName = prompt('请输入文件夹名称:');
        if (folderName) {
            // 确保父文件夹是展开的
            if (parentItem.classList.contains('folder-item') && parentItem.dataset.expanded === 'false') {
                this.toggleFolder(parentItem);
            }

            // 创建新文件夹项
            const newFolderItem = document.createElement('div');
            newFolderItem.className = 'tree-item folder-item';
            newFolderItem.dataset.path = `${parentItem.dataset.path}/${folderName}`;
            newFolderItem.dataset.expanded = 'false';

            newFolderItem.innerHTML = `
                <div class="tree-item-content">
                    <i class="fas fa-chevron-right expand-icon"></i>
                    <i class="fas fa-folder folder-icon"></i>
                    <span class="item-name">${folderName}</span>
                </div>
                <div class="tree-children" style="display: none;"></div>
            `;

            // 添加展开/收缩事件
            const content = newFolderItem.querySelector('.tree-item-content');
            content.addEventListener('click', (e) => {
                e.stopPropagation();
                this.toggleFolder(newFolderItem);
            });

            // 添加到父文件夹中
            const children = parentItem.querySelector('.tree-children');
            if (children) {
                children.appendChild(newFolderItem);
            }

            console.log(`在 ${parentItem.dataset.path} 中创建文件夹: ${folderName}`);
        }
    }

    // 复制项目路径
    copyItemPath(item) {
        const path = item.dataset.path || item.dataset.file;
        if (path) {
            navigator.clipboard.writeText(path).then(() => {
                console.log(`路径已复制: ${path}`);
                // 这里可以显示一个提示消息
            }).catch(err => {
                console.error('复制路径失败:', err);
            });
        }
    }

    // 设置标签页右键菜单
    setupTabContextMenu() {
        // 标签页右键菜单项点击事件
        document.querySelectorAll('#tabContextMenu .context-menu-item').forEach(item => {
            item.addEventListener('click', () => {
                // 如果菜单项被禁用，不执行操作
                if (item.classList.contains('disabled')) {
                    return;
                }

                const action = item.dataset.action;
                this.handleTabContextMenuAction(action, this.currentContextTab);
                this.hideTabContextMenu();
            });
        });

        // 存储当前右键的标签页
        this.currentContextTab = null;
    }

    // 显示标签页右键菜单
    showTabContextMenu(x, y, tab) {
        const tabContextMenu = document.getElementById('tabContextMenu');
        this.currentContextTab = tab;

        // 获取标签页在标签栏中的位置
        const tabBar = document.querySelector('.tab-bar');
        const tabs = Array.from(tabBar.querySelectorAll('.tab'));
        const tabIndex = tabs.indexOf(tab);

        // 根据标签页位置启用/禁用菜单项
        const closeLeftItem = tabContextMenu.querySelector('[data-action="close-left"]');
        const closeRightItem = tabContextMenu.querySelector('[data-action="close-right"]');
        const closeOthersItem = tabContextMenu.querySelector('[data-action="close-others"]');

        // 如果是第一个标签页，禁用"关闭左边标签"
        if (tabIndex === 0) {
            closeLeftItem.classList.add('disabled');
        } else {
            closeLeftItem.classList.remove('disabled');
        }

        // 如果是最后一个标签页，禁用"关闭右边标签"
        if (tabIndex === tabs.length - 1) {
            closeRightItem.classList.add('disabled');
        } else {
            closeRightItem.classList.remove('disabled');
        }

        // 如果只有一个标签页，禁用"关闭其他标签"
        if (tabs.length === 1) {
            closeOthersItem.classList.add('disabled');
        } else {
            closeOthersItem.classList.remove('disabled');
        }

        // 设置菜单位置
        tabContextMenu.style.left = x + 'px';
        tabContextMenu.style.top = y + 'px';

        // 显示菜单
        tabContextMenu.classList.add('show');

        // 确保菜单在视窗内
        const rect = tabContextMenu.getBoundingClientRect();
        if (rect.right > window.innerWidth) {
            tabContextMenu.style.left = (x - rect.width) + 'px';
        }
        if (rect.bottom > window.innerHeight) {
            tabContextMenu.style.top = (y - rect.height) + 'px';
        }
    }

    // 隐藏标签页右键菜单
    hideTabContextMenu() {
        const tabContextMenu = document.getElementById('tabContextMenu');
        tabContextMenu.classList.remove('show');
        this.currentContextTab = null;
    }

    // 处理标签页右键菜单动作
    handleTabContextMenuAction(action, tab) {
        if (!tab) return;

        const fileName = tab.dataset.file;

        switch (action) {
            case 'close-current':
                this.closeTab(fileName);
                break;
            case 'close-others':
                this.closeOtherTabs(fileName);
                break;
            case 'close-left':
                this.closeLeftTabs(fileName);
                break;
            case 'close-right':
                this.closeRightTabs(fileName);
                break;
            case 'close-all':
                this.closeAllTabs();
                break;
            case 'copy-file-path':
                this.copyFilePath(fileName);
                break;
            case 'reveal-in-explorer':
                this.revealInExplorer(fileName);
                break;
            default:
                console.log(`未实现的标签页菜单动作: ${action}`);
        }
    }

    // 关闭其他标签页
    closeOtherTabs(keepFileName) {
        const tabsToClose = [...this.openTabs];
        tabsToClose.forEach(fileName => {
            if (fileName !== keepFileName) {
                this.closeTab(fileName);
            }
        });
    }

    // 关闭左边的标签页
    closeLeftTabs(targetFileName) {
        const tabBar = document.querySelector('.tab-bar');
        const tabs = Array.from(tabBar.querySelectorAll('.tab'));
        const targetTab = tabs.find(tab => tab.dataset.file === targetFileName);
        const targetIndex = tabs.indexOf(targetTab);

        // 关闭目标标签页左边的所有标签页
        for (let i = 0; i < targetIndex; i++) {
            const fileName = tabs[i].dataset.file;
            this.closeTab(fileName);
        }
    }

    // 关闭右边的标签页
    closeRightTabs(targetFileName) {
        const tabBar = document.querySelector('.tab-bar');
        const tabs = Array.from(tabBar.querySelectorAll('.tab'));
        const targetTab = tabs.find(tab => tab.dataset.file === targetFileName);
        const targetIndex = tabs.indexOf(targetTab);

        // 关闭目标标签页右边的所有标签页
        for (let i = targetIndex + 1; i < tabs.length; i++) {
            const fileName = tabs[i].dataset.file;
            this.closeTab(fileName);
        }
    }

    // 关闭所有标签页
    closeAllTabs() {
        const tabsToClose = [...this.openTabs];
        tabsToClose.forEach(fileName => {
            this.closeTab(fileName);
        });
    }

    // 复制文件路径
    copyFilePath(fileName) {
        const path = `project/${fileName}`;
        navigator.clipboard.writeText(path).then(() => {
            console.log(`文件路径已复制: ${path}`);
            // 这里可以显示一个提示消息
        }).catch(err => {
            console.error('复制文件路径失败:', err);
        });
    }

    // 在资源管理器中显示
    revealInExplorer(fileName) {
        // 高亮文件在文件树中的位置
        this.highlightFileInTree(fileName);

        // 确保资源管理器面板是打开的
        this.switchPanel('explorer');

        console.log(`在资源管理器中显示: ${fileName}`);
    }

    // 设置代码编辑器右键菜单
    setupEditorContextMenu() {
        // 代码编辑器右键菜单项点击事件
        document.querySelectorAll('#editorContextMenu .context-menu-item').forEach(item => {
            item.addEventListener('click', () => {
                // 如果菜单项被禁用，不执行操作
                if (item.classList.contains('disabled')) {
                    return;
                }

                const action = item.dataset.action;
                this.handleEditorContextMenuAction(action);
                this.hideEditorContextMenu();
            });
        });
    }

    // 显示代码编辑器右键菜单
    showEditorContextMenu(x, y) {
        const editorContextMenu = document.getElementById('editorContextMenu');
        const codeEditor = document.querySelector('.code-editor');

        // 检查编辑器状态，启用/禁用相应菜单项
        const hasSelection = codeEditor.selectionStart !== codeEditor.selectionEnd;
        const hasContent = codeEditor.value.length > 0;

        // 根据状态启用/禁用菜单项
        const cutItem = editorContextMenu.querySelector('[data-action="cut"]');
        const copyItem = editorContextMenu.querySelector('[data-action="copy"]');
        const selectAllItem = editorContextMenu.querySelector('[data-action="select-all"]');

        // 如果没有选中文本，禁用剪切和复制
        if (hasSelection) {
            cutItem.classList.remove('disabled');
            copyItem.classList.remove('disabled');
        } else {
            cutItem.classList.add('disabled');
            copyItem.classList.add('disabled');
        }

        // 如果没有内容，禁用全选
        if (hasContent) {
            selectAllItem.classList.remove('disabled');
        } else {
            selectAllItem.classList.add('disabled');
        }

        // 设置菜单位置
        editorContextMenu.style.left = x + 'px';
        editorContextMenu.style.top = y + 'px';

        // 显示菜单
        editorContextMenu.classList.add('show');

        // 确保菜单在视窗内
        const rect = editorContextMenu.getBoundingClientRect();
        if (rect.right > window.innerWidth) {
            editorContextMenu.style.left = (x - rect.width) + 'px';
        }
        if (rect.bottom > window.innerHeight) {
            editorContextMenu.style.top = (y - rect.height) + 'px';
        }
    }

    // 隐藏代码编辑器右键菜单
    hideEditorContextMenu() {
        const editorContextMenu = document.getElementById('editorContextMenu');
        editorContextMenu.classList.remove('show');
    }

    // 处理代码编辑器右键菜单动作
    handleEditorContextMenuAction(action) {
        const codeEditor = document.querySelector('.code-editor');

        switch (action) {
            case 'undo':
                this.editorUndo();
                break;
            case 'redo':
                this.editorRedo();
                break;
            case 'cut':
                this.editorCut();
                break;
            case 'copy':
                this.editorCopy();
                break;
            case 'paste':
                this.editorPaste();
                break;
            case 'select-all':
                codeEditor.select();
                break;
            case 'select-line':
                this.selectCurrentLine();
                break;
            case 'find':
                this.switchPanel('search');
                document.querySelector('.search-input').focus();
                break;
            case 'replace':
                this.switchPanel('search');
                document.querySelector('.replace-input').focus();
                break;
            case 'go-to-line':
                this.goToLine();
                break;
            case 'toggle-comment':
                this.toggleComment();
                break;
            case 'format-document':
                this.formatDocument();
                break;
            default:
                console.log(`未实现的编辑器菜单动作: ${action}`);
        }
    }

    // 编辑器撤销功能
    editorUndo() {
        const codeEditor = document.querySelector('.code-editor');
        if (this.undoStack && this.undoStack.length > 0) {
            const previousState = this.undoStack.pop();
            this.redoStack = this.redoStack || [];
            this.redoStack.push(codeEditor.value);
            codeEditor.value = previousState;
            this.updateLineNumbers();
        }
    }

    // 编辑器重做功能
    editorRedo() {
        const codeEditor = document.querySelector('.code-editor');
        if (this.redoStack && this.redoStack.length > 0) {
            const nextState = this.redoStack.pop();
            this.undoStack = this.undoStack || [];
            this.undoStack.push(codeEditor.value);
            codeEditor.value = nextState;
            this.updateLineNumbers();
        }
    }

    // 编辑器剪切功能
    editorCut() {
        const codeEditor = document.querySelector('.code-editor');
        const selectedText = codeEditor.value.substring(codeEditor.selectionStart, codeEditor.selectionEnd);

        if (selectedText) {
            // 复制到剪贴板
            navigator.clipboard.writeText(selectedText).then(() => {
                // 删除选中的文本
                const start = codeEditor.selectionStart;
                const end = codeEditor.selectionEnd;
                const newValue = codeEditor.value.substring(0, start) + codeEditor.value.substring(end);
                codeEditor.value = newValue;
                codeEditor.setSelectionRange(start, start);
                this.updateLineNumbers();
                this.saveCurrentFile();
            });
        }
    }

    // 编辑器复制功能
    editorCopy() {
        const codeEditor = document.querySelector('.code-editor');
        const selectedText = codeEditor.value.substring(codeEditor.selectionStart, codeEditor.selectionEnd);

        if (selectedText) {
            navigator.clipboard.writeText(selectedText).then(() => {
                console.log('文本已复制到剪贴板');
            });
        }
    }

    // 编辑器粘贴功能
    editorPaste() {
        const codeEditor = document.querySelector('.code-editor');

        navigator.clipboard.readText().then(text => {
            const start = codeEditor.selectionStart;
            const end = codeEditor.selectionEnd;
            const newValue = codeEditor.value.substring(0, start) + text + codeEditor.value.substring(end);
            codeEditor.value = newValue;
            codeEditor.setSelectionRange(start + text.length, start + text.length);
            this.updateLineNumbers();
            this.saveCurrentFile();
        }).catch(err => {
            console.error('粘贴失败:', err);
        });
    }

    // 切换注释
    toggleComment() {
        const codeEditor = document.querySelector('.code-editor');
        const start = codeEditor.selectionStart;
        const end = codeEditor.selectionEnd;
        const text = codeEditor.value;

        // 找到选中文本的行
        const beforeText = text.substring(0, start);
        const selectedText = text.substring(start, end);
        const afterText = text.substring(end);

        const lines = selectedText.split('\n');
        const commentedLines = lines.map(line => {
            if (line.trim().startsWith('//')) {
                // 取消注释
                return line.replace(/^\s*\/\/\s?/, '');
            } else if (line.trim().length > 0) {
                // 添加注释
                return '// ' + line;
            }
            return line;
        });

        const newSelectedText = commentedLines.join('\n');
        const newValue = beforeText + newSelectedText + afterText;

        codeEditor.value = newValue;
        codeEditor.setSelectionRange(start, start + newSelectedText.length);
        this.updateLineNumbers();
        this.saveCurrentFile();
    }

    // 格式化文档
    formatDocument() {
        const codeEditor = document.querySelector('.code-editor');
        let content = codeEditor.value;

        // 简单的格式化逻辑（可以根据文件类型扩展）
        if (this.currentFile && this.currentFile.endsWith('.html')) {
            content = this.formatHTML(content);
        } else if (this.currentFile && this.currentFile.endsWith('.css')) {
            content = this.formatCSS(content);
        } else if (this.currentFile && this.currentFile.endsWith('.js')) {
            content = this.formatJavaScript(content);
        }

        codeEditor.value = content;
        this.updateLineNumbers();
        this.saveCurrentFile();
    }

    // 简单的HTML格式化
    formatHTML(html) {
        // 这里可以实现更复杂的HTML格式化逻辑
        return html.replace(/></g, '>\n<').replace(/^\s+|\s+$/gm, '');
    }

    // 简单的CSS格式化
    formatCSS(css) {
        // 这里可以实现更复杂的CSS格式化逻辑
        return css.replace(/\{/g, ' {\n    ').replace(/\}/g, '\n}\n').replace(/;/g, ';\n    ');
    }

    // 简单的JavaScript格式化
    formatJavaScript(js) {
        // 这里可以实现更复杂的JavaScript格式化逻辑
        return js.replace(/\{/g, ' {\n    ').replace(/\}/g, '\n}\n');
    }

    // 最近文件管理
    loadRecentFiles() {
        const stored = localStorage.getItem('vscode-editor-recent-files');
        return stored ? JSON.parse(stored) : [];
    }

    saveRecentFiles() {
        localStorage.setItem('vscode-editor-recent-files', JSON.stringify(this.recentFiles));
    }

    addToRecentFiles(fileName) {
        // 移除已存在的项目
        this.recentFiles = this.recentFiles.filter(item => item.name !== fileName);

        // 添加到开头
        this.recentFiles.unshift({
            name: fileName,
            path: `project/${fileName}`,
            timestamp: Date.now()
        });

        // 限制最大数量
        if (this.recentFiles.length > 10) {
            this.recentFiles = this.recentFiles.slice(0, 10);
        }

        this.saveRecentFiles();
        this.updateRecentFiles();
    }

    updateRecentFiles() {
        const recentFilesContainer = document.getElementById('recentFiles');
        if (!recentFilesContainer) return;

        recentFilesContainer.innerHTML = '';

        if (this.recentFiles.length === 0) {
            recentFilesContainer.innerHTML = '<p style="color: #858585; text-align: center; padding: 20px;">暂无最近文件</p>';
            return;
        }

        this.recentFiles.forEach(file => {
            const recentItem = document.createElement('div');
            recentItem.className = 'recent-item';
            recentItem.dataset.file = file.name;

            const fileIcon = this.getFileIcon(file.name);
            const timeAgo = this.getTimeAgo(file.timestamp);

            recentItem.innerHTML = `
                <i class="${fileIcon}"></i>
                <div class="recent-info">
                    <span class="recent-name">${file.name}</span>
                    <span class="recent-path">${file.path}</span>
                </div>
                <span class="recent-time">${timeAgo}</span>
            `;

            recentItem.addEventListener('click', () => {
                if (this.files[file.name]) {
                    this.openFile(file.name);
                }
            });

            recentFilesContainer.appendChild(recentItem);
        });
    }

    getTimeAgo(timestamp) {
        const now = Date.now();
        const diff = now - timestamp;
        const minutes = Math.floor(diff / 60000);
        const hours = Math.floor(diff / 3600000);
        const days = Math.floor(diff / 86400000);

        if (minutes < 1) return '刚刚';
        if (minutes < 60) return `${minutes}分钟前`;
        if (hours < 24) return `${hours}小时前`;
        return `${days}天前`;
    }

    clearRecentFiles() {
        if (confirm('确定要清除所有最近文件记录吗？')) {
            this.recentFiles = [];
            this.saveRecentFiles();
            this.updateRecentFiles();
        }
    }

    showMoreRecentFiles() {
        alert('显示更多最近文件功能待实现');
    }

    // 欢迎页面其他功能
    openFolderDialog() {
        alert('打开文件夹功能待实现');
    }

    cloneRepository() {
        const repoUrl = prompt('请输入Git仓库URL:');
        if (repoUrl) {
            alert(`克隆仓库功能待实现: ${repoUrl}`);
        }
    }

    showDocumentation() {
        window.open('https://code.visualstudio.com/docs', '_blank');
    }

    showTipsAndTricks() {
        alert('提示和技巧:\n\n1. 使用 Ctrl+P 快速打开文件\n2. 使用 Ctrl+Shift+P 打开命令面板\n3. 使用 Ctrl+/ 切换注释\n4. 使用 Alt+↑/↓ 移动行\n5. 使用 Shift+Alt+↑/↓ 复制行');
    }

    showChangelog() {
        alert('更新日志:\n\nv1.0.0\n- 初始版本发布\n- VS Code风格界面\n- 基础编辑功能\n- 文件管理\n- 响应式设计');
    }

    showSettings() {
        alert('设置功能待实现');
    }
}

// 初始化编辑器
document.addEventListener('DOMContentLoaded', () => {
    new VSCodeEditor();
});
