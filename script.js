// VS Code 风格编辑器 JavaScript 功能

class VSCodeEditor {
    constructor() {
        this.currentFile = 'index.html';
        this.files = {
            'index.html': '<!DOCTYPE html>\n<html lang="zh-CN">\n<head>\n    <meta charset="UTF-8">\n    <meta name="viewport" content="width=device-width, initial-scale=1.0">\n    <title>VS Code Editor</title>\n</head>\n<body>\n    <h1>Hello World!</h1>\n</body>\n</html>',
            'styles.css': '/* CSS 样式 */\nbody {\n    font-family: Arial, sans-serif;\n    margin: 0;\n    padding: 20px;\n}\n\nh1 {\n    color: #333;\n    text-align: center;\n}',
            'script.js': '// JavaScript 代码\nconsole.log("Hello World!");\n\nfunction greet(name) {\n    return `Hello, ${name}!`;\n}\n\ngreet("VS Code Editor");',
            'README.md': '# VS Code Editor\n\n这是一个模仿 VS Code 风格的编辑器。\n\n## 功能特性\n\n- 文件管理\n- 代码编辑\n- 语法高亮\n- 标签页切换\n- 响应式设计'
        };
        this.openTabs = ['index.html', 'styles.css'];
        this.isMobile = window.innerWidth <= 768;
        this.sidebarOpen = false;
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.updateLineNumbers();
        this.loadFile(this.currentFile);
    }

    setupEventListeners() {
        // 活动栏切换
        document.querySelectorAll('.activity-item').forEach(item => {
            item.addEventListener('click', (e) => {
                this.switchPanel(e.currentTarget.dataset.panel);
                if (this.isMobile) {
                    this.toggleSidebar();
                }
            });
        });

        // 文件点击
        document.querySelectorAll('.file-item').forEach(item => {
            item.addEventListener('click', (e) => {
                const fileName = e.currentTarget.dataset.file;
                this.openFile(fileName);
            });
        });

        // 标签页切换
        document.querySelectorAll('.tab').forEach(tab => {
            tab.addEventListener('click', (e) => {
                if (!e.target.classList.contains('tab-close')) {
                    const fileName = e.currentTarget.dataset.file;
                    this.switchTab(fileName);
                }
            });
        });

        // 标签页关闭
        document.querySelectorAll('.tab-close').forEach(closeBtn => {
            closeBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                const fileName = e.currentTarget.closest('.tab').dataset.file;
                this.closeTab(fileName);
            });
        });

        // 代码编辑器
        const codeEditor = document.querySelector('.code-editor');
        codeEditor.addEventListener('input', () => {
            this.updateLineNumbers();
            this.saveCurrentFile();
        });

        codeEditor.addEventListener('scroll', () => {
            this.syncLineNumbers();
        });

        // 新建文件
        document.querySelector('.fa-file-plus').addEventListener('click', () => {
            this.createNewFile();
        });

        // 搜索功能
        const searchInput = document.querySelector('.search-input');
        searchInput.addEventListener('input', (e) => {
            this.searchInCode(e.target.value);
        });

        // 窗口控制
        document.querySelector('.control.minimize').addEventListener('click', () => {
            console.log('最小化窗口');
        });

        document.querySelector('.control.maximize').addEventListener('click', () => {
            console.log('最大化窗口');
        });

        document.querySelector('.control.close').addEventListener('click', () => {
            console.log('关闭窗口');
        });

        // 侧边栏遮罩点击
        document.querySelector('.sidebar-overlay').addEventListener('click', () => {
            this.closeSidebar();
        });

        // 窗口大小改变
        window.addEventListener('resize', () => {
            this.isMobile = window.innerWidth <= 768;
            if (!this.isMobile) {
                this.closeSidebar();
            }
        });

        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            this.handleKeyboardShortcuts(e);
        });

        // 菜单项点击
        document.querySelectorAll('.menu-item').forEach(item => {
            item.addEventListener('click', (e) => {
                e.stopPropagation();
                this.toggleMenu(item);
            });
        });

        // 菜单选项点击
        document.querySelectorAll('.menu-option').forEach(option => {
            option.addEventListener('click', (e) => {
                e.stopPropagation();
                const action = option.dataset.action;
                this.handleMenuAction(action);
                this.closeAllMenus();
            });
        });

        // 点击其他地方关闭菜单
        document.addEventListener('click', () => {
            this.closeAllMenus();
        });
    }

    switchPanel(panelName) {
        // 更新活动栏状态
        document.querySelectorAll('.activity-item').forEach(item => {
            item.classList.remove('active');
        });
        document.querySelector(`[data-panel="${panelName}"]`).classList.add('active');

        // 切换面板
        document.querySelectorAll('.panel').forEach(panel => {
            panel.classList.remove('active');
        });
        document.querySelector(`.${panelName}-panel`).classList.add('active');
    }

    openFile(fileName) {
        if (!this.openTabs.includes(fileName)) {
            this.openTabs.push(fileName);
            this.createTab(fileName);
        }
        this.switchTab(fileName);
    }

    createTab(fileName) {
        const tabBar = document.querySelector('.tab-bar');
        const tab = document.createElement('div');
        tab.className = 'tab';
        tab.dataset.file = fileName;
        
        const fileIcon = this.getFileIcon(fileName);
        tab.innerHTML = `
            <i class="${fileIcon}"></i>
            <span class="tab-name">${fileName}</span>
            <i class="fas fa-times tab-close"></i>
        `;

        // 添加事件监听器
        tab.addEventListener('click', (e) => {
            if (!e.target.classList.contains('tab-close')) {
                this.switchTab(fileName);
            }
        });

        tab.querySelector('.tab-close').addEventListener('click', (e) => {
            e.stopPropagation();
            this.closeTab(fileName);
        });

        tabBar.appendChild(tab);
    }

    switchTab(fileName) {
        // 保存当前文件
        this.saveCurrentFile();

        // 更新当前文件
        this.currentFile = fileName;

        // 更新标签页状态
        document.querySelectorAll('.tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-file="${fileName}"]`).classList.add('active');

        // 更新文件列表状态
        document.querySelectorAll('.file-item').forEach(item => {
            item.classList.remove('active');
        });
        document.querySelector(`.file-item[data-file="${fileName}"]`).classList.add('active');

        // 加载文件内容
        this.loadFile(fileName);
    }

    closeTab(fileName) {
        const tabIndex = this.openTabs.indexOf(fileName);
        if (tabIndex > -1) {
            this.openTabs.splice(tabIndex, 1);
            document.querySelector(`[data-file="${fileName}"]`).remove();

            // 如果关闭的是当前标签页，切换到其他标签页
            if (fileName === this.currentFile && this.openTabs.length > 0) {
                this.switchTab(this.openTabs[this.openTabs.length - 1]);
            } else if (this.openTabs.length === 0) {
                this.currentFile = null;
                document.querySelector('.code-editor').value = '';
                this.updateLineNumbers();
            }
        }
    }

    loadFile(fileName) {
        const codeEditor = document.querySelector('.code-editor');
        codeEditor.value = this.files[fileName] || '';
        this.updateLineNumbers();
        this.updateStatusBar(fileName);
    }

    saveCurrentFile() {
        if (this.currentFile) {
            const codeEditor = document.querySelector('.code-editor');
            this.files[this.currentFile] = codeEditor.value;
        }
    }

    updateLineNumbers() {
        const codeEditor = document.querySelector('.code-editor');
        const lineNumbers = document.querySelector('.line-numbers');
        const lines = codeEditor.value.split('\n');
        
        lineNumbers.innerHTML = '';
        for (let i = 1; i <= Math.max(lines.length, 10); i++) {
            const lineNumber = document.createElement('div');
            lineNumber.className = 'line-number';
            lineNumber.textContent = i;
            lineNumbers.appendChild(lineNumber);
        }
    }

    syncLineNumbers() {
        const codeEditor = document.querySelector('.code-editor');
        const lineNumbers = document.querySelector('.line-numbers');
        lineNumbers.scrollTop = codeEditor.scrollTop;
    }

    updateStatusBar(fileName) {
        const fileType = this.getFileType(fileName);
        const statusRight = document.querySelector('.status-right');
        const fileTypeElement = statusRight.children[2];
        fileTypeElement.textContent = fileType;
    }

    getFileIcon(fileName) {
        const extension = fileName.split('.').pop().toLowerCase();
        const iconMap = {
            'html': 'fab fa-html5',
            'css': 'fab fa-css3-alt',
            'js': 'fab fa-js-square',
            'md': 'fab fa-markdown',
            'json': 'fas fa-code',
            'txt': 'fas fa-file-alt'
        };
        return iconMap[extension] || 'fas fa-file';
    }

    getFileType(fileName) {
        const extension = fileName.split('.').pop().toLowerCase();
        const typeMap = {
            'html': 'HTML',
            'css': 'CSS',
            'js': 'JavaScript',
            'md': 'Markdown',
            'json': 'JSON',
            'txt': 'Plain Text'
        };
        return typeMap[extension] || 'Unknown';
    }

    createNewFile() {
        const fileName = prompt('请输入文件名:');
        if (fileName && !this.files[fileName]) {
            this.files[fileName] = '';
            
            // 添加到文件列表
            const fileList = document.querySelector('.file-list');
            const fileItem = document.createElement('div');
            fileItem.className = 'file-item';
            fileItem.dataset.file = fileName;
            
            const fileIcon = this.getFileIcon(fileName);
            fileItem.innerHTML = `
                <i class="${fileIcon}"></i>
                <span>${fileName}</span>
            `;
            
            fileItem.addEventListener('click', () => {
                this.openFile(fileName);
            });
            
            fileList.appendChild(fileItem);
            this.openFile(fileName);
        }
    }

    searchInCode(searchTerm) {
        const codeEditor = document.querySelector('.code-editor');
        const content = codeEditor.value;

        if (searchTerm) {
            // 简单的搜索高亮（实际项目中可以使用更复杂的实现）
            const regex = new RegExp(searchTerm, 'gi');
            const matches = content.match(regex);
            console.log(`找到 ${matches ? matches.length : 0} 个匹配项`);
        }
    }

    toggleSidebar() {
        const sidebar = document.querySelector('.sidebar');
        const overlay = document.querySelector('.sidebar-overlay');

        if (this.sidebarOpen) {
            this.closeSidebar();
        } else {
            this.openSidebar();
        }
    }

    openSidebar() {
        const sidebar = document.querySelector('.sidebar');
        const overlay = document.querySelector('.sidebar-overlay');

        sidebar.classList.add('mobile-open');
        overlay.classList.add('active');
        this.sidebarOpen = true;
    }

    closeSidebar() {
        const sidebar = document.querySelector('.sidebar');
        const overlay = document.querySelector('.sidebar-overlay');

        sidebar.classList.remove('mobile-open');
        overlay.classList.remove('active');
        this.sidebarOpen = false;
    }

    handleKeyboardShortcuts(e) {
        // 关闭菜单
        if (e.key === 'Escape') {
            this.closeAllMenus();
            if (this.isMobile && this.sidebarOpen) {
                this.closeSidebar();
            }
        }

        // Ctrl+S 保存
        if (e.ctrlKey && e.key === 's') {
            e.preventDefault();
            this.saveCurrentFile();
            console.log('文件已保存');
        }

        // Ctrl+N 新建文件
        if (e.ctrlKey && e.key === 'n') {
            e.preventDefault();
            this.createNewFile();
        }

        // Ctrl+O 打开文件
        if (e.ctrlKey && e.key === 'o') {
            e.preventDefault();
            this.openFileDialog();
        }

        // Ctrl+W 关闭标签页
        if (e.ctrlKey && e.key === 'w') {
            e.preventDefault();
            if (this.currentFile) {
                this.closeTab(this.currentFile);
            }
        }

        // Ctrl+F 搜索
        if (e.ctrlKey && e.key === 'f') {
            e.preventDefault();
            this.switchPanel('search');
            document.querySelector('.search-input').focus();
        }

        // Ctrl+H 替换
        if (e.ctrlKey && e.key === 'h') {
            e.preventDefault();
            this.switchPanel('search');
            document.querySelector('.replace-input').focus();
        }

        // Ctrl+B 切换侧边栏
        if (e.ctrlKey && e.key === 'b') {
            e.preventDefault();
            this.toggleSidebarVisibility();
        }

        // Ctrl+G 转到行
        if (e.ctrlKey && e.key === 'g') {
            e.preventDefault();
            this.goToLine();
        }

        // Ctrl+P 转到文件
        if (e.ctrlKey && e.key === 'p') {
            e.preventDefault();
            this.goToFile();
        }

        // Ctrl+L 选择行
        if (e.ctrlKey && e.key === 'l') {
            e.preventDefault();
            this.selectCurrentLine();
        }

        // 缩放快捷键
        if (e.ctrlKey && e.key === '=') {
            e.preventDefault();
            this.adjustZoom(1.1);
        }

        if (e.ctrlKey && e.key === '-') {
            e.preventDefault();
            this.adjustZoom(0.9);
        }

        if (e.ctrlKey && e.key === '0') {
            e.preventDefault();
            this.resetZoom();
        }

        // F5 运行文件
        if (e.key === 'F5') {
            e.preventDefault();
            this.runCurrentFile();
        }
    }

    // 简单的语法高亮
    applySyntaxHighlighting() {
        const codeEditor = document.querySelector('.code-editor');
        const content = codeEditor.value;

        // 这里可以实现更复杂的语法高亮逻辑
        // 目前只是一个示例
        console.log('应用语法高亮');
    }

    // 菜单控制
    toggleMenu(menuItem) {
        const isActive = menuItem.classList.contains('active');

        // 关闭所有菜单
        this.closeAllMenus();

        // 如果当前菜单未激活，则激活它
        if (!isActive) {
            menuItem.classList.add('active');
        }
    }

    closeAllMenus() {
        document.querySelectorAll('.menu-item').forEach(item => {
            item.classList.remove('active');
        });
    }

    // 处理菜单动作
    handleMenuAction(action) {
        switch (action) {
            case 'new-file':
                this.createNewFile();
                break;
            case 'open-file':
                this.openFileDialog();
                break;
            case 'save':
                this.saveCurrentFile();
                console.log('文件已保存');
                break;
            case 'save-as':
                this.saveAsFile();
                break;
            case 'close-file':
                if (this.currentFile) {
                    this.closeTab(this.currentFile);
                }
                break;
            case 'undo':
                document.execCommand('undo');
                break;
            case 'redo':
                document.execCommand('redo');
                break;
            case 'cut':
                document.execCommand('cut');
                break;
            case 'copy':
                document.execCommand('copy');
                break;
            case 'paste':
                document.execCommand('paste');
                break;
            case 'find':
                this.switchPanel('search');
                document.querySelector('.search-input').focus();
                break;
            case 'replace':
                this.switchPanel('search');
                document.querySelector('.replace-input').focus();
                break;
            case 'select-all':
                document.querySelector('.code-editor').select();
                break;
            case 'select-line':
                this.selectCurrentLine();
                break;
            case 'toggle-sidebar':
                this.toggleSidebarVisibility();
                break;
            case 'toggle-panel':
                console.log('切换面板');
                break;
            case 'zoom-in':
                this.adjustZoom(1.1);
                break;
            case 'zoom-out':
                this.adjustZoom(0.9);
                break;
            case 'reset-zoom':
                this.resetZoom();
                break;
            case 'go-to-line':
                this.goToLine();
                break;
            case 'go-to-file':
                this.goToFile();
                break;
            case 'run-file':
                this.runCurrentFile();
                break;
            case 'new-terminal':
                console.log('新建终端');
                break;
            case 'about':
                this.showAbout();
                break;
            case 'shortcuts':
                this.showShortcuts();
                break;
            default:
                console.log(`未实现的动作: ${action}`);
        }
    }

    // 新增的菜单功能方法
    openFileDialog() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.txt,.js,.html,.css,.md,.json';
        input.onchange = (e) => {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    this.files[file.name] = e.target.result;
                    this.addFileToExplorer(file.name);
                    this.openFile(file.name);
                };
                reader.readAsText(file);
            }
        };
        input.click();
    }

    saveAsFile() {
        if (this.currentFile) {
            const content = this.files[this.currentFile];
            const blob = new Blob([content], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = this.currentFile;
            a.click();
            URL.revokeObjectURL(url);
        }
    }

    selectCurrentLine() {
        const editor = document.querySelector('.code-editor');
        const start = editor.selectionStart;
        const value = editor.value;

        // 找到当前行的开始和结束
        let lineStart = value.lastIndexOf('\n', start - 1) + 1;
        let lineEnd = value.indexOf('\n', start);
        if (lineEnd === -1) lineEnd = value.length;

        editor.setSelectionRange(lineStart, lineEnd);
    }

    toggleSidebarVisibility() {
        const sidebar = document.querySelector('.sidebar');
        sidebar.style.display = sidebar.style.display === 'none' ? 'block' : 'none';
    }

    adjustZoom(factor) {
        const editor = document.querySelector('.code-editor');
        const currentSize = parseFloat(getComputedStyle(editor).fontSize);
        editor.style.fontSize = (currentSize * factor) + 'px';
    }

    resetZoom() {
        const editor = document.querySelector('.code-editor');
        editor.style.fontSize = '14px';
    }

    goToLine() {
        const lineNumber = prompt('转到行号:');
        if (lineNumber && !isNaN(lineNumber)) {
            const editor = document.querySelector('.code-editor');
            const lines = editor.value.split('\n');
            const targetLine = Math.min(Math.max(1, parseInt(lineNumber)), lines.length);

            // 计算目标位置
            let position = 0;
            for (let i = 0; i < targetLine - 1; i++) {
                position += lines[i].length + 1; // +1 for newline
            }

            editor.focus();
            editor.setSelectionRange(position, position);
        }
    }

    goToFile() {
        const fileName = prompt('输入文件名:');
        if (fileName && this.files[fileName]) {
            this.openFile(fileName);
        } else if (fileName) {
            alert('文件不存在');
        }
    }

    runCurrentFile() {
        if (this.currentFile && this.currentFile.endsWith('.html')) {
            const content = this.files[this.currentFile];
            const newWindow = window.open();
            newWindow.document.write(content);
        } else {
            console.log('运行当前文件');
        }
    }

    showAbout() {
        alert('VS Code 风格编辑器\n\n版本: 1.0.0\n使用 HTML + CSS + JavaScript 构建');
    }

    showShortcuts() {
        const shortcuts = `
键盘快捷键:

文件操作:
Ctrl+N - 新建文件
Ctrl+O - 打开文件
Ctrl+S - 保存文件
Ctrl+W - 关闭文件

编辑操作:
Ctrl+Z - 撤销
Ctrl+Y - 重做
Ctrl+X - 剪切
Ctrl+C - 复制
Ctrl+V - 粘贴
Ctrl+A - 全选
Ctrl+F - 查找
Ctrl+H - 替换

查看操作:
Ctrl+B - 切换侧边栏
Ctrl++ - 放大
Ctrl+- - 缩小
Ctrl+0 - 重置缩放

导航操作:
Ctrl+G - 转到行
Ctrl+P - 转到文件
        `;
        alert(shortcuts);
    }

    addFileToExplorer(fileName) {
        const fileList = document.querySelector('.file-list');
        const existingFile = fileList.querySelector(`[data-file="${fileName}"]`);

        if (!existingFile) {
            const fileItem = document.createElement('div');
            fileItem.className = 'file-item';
            fileItem.dataset.file = fileName;

            const fileIcon = this.getFileIcon(fileName);
            fileItem.innerHTML = `
                <i class="${fileIcon}"></i>
                <span>${fileName}</span>
            `;

            fileItem.addEventListener('click', () => {
                this.openFile(fileName);
            });

            fileList.appendChild(fileItem);
        }
    }
}

// 初始化编辑器
document.addEventListener('DOMContentLoaded', () => {
    new VSCodeEditor();
});
